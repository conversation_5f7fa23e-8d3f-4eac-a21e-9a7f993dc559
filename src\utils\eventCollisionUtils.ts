import { CalendarEvent } from '@/typings/page';
import { EventSegment } from './multiDayEventUtils';
import { isSameDay } from 'date-fns';

// --- NEW CONSTANTS ---
const GUTTER_WIDTH_PERCENT = 10; // Space on the right for creating new events
const CASCADE_STAGGER_PERCENT = 15; // How much to offset each cascading event

export interface SegmentLayout {
  segment: EventSegment;
  left: number;
  width: number;
  zIndex: number;
  hasOverlap: boolean; // New property to indicate if this event overlaps with others
}

export interface FinalLayout {
  segmentLayouts: SegmentLayout[];
}

/**
 * Checks if two event segments overlap in time on the same day.
 */
const segmentsOverlap = (segment1: EventSegment, segment2: EventSegment): boolean => {
  // Must be on the same day
  if (!isSameDay(segment1.date, segment2.date)) {
    return false;
  }
  return segment1.startTime < segment2.endTime && segment2.startTime < segment1.endTime;
};

/**
 * The primary function to calculate layout for event segments, incorporating a "+N more" button
 * and a gutter for creating new events.
 */
export const calculateLayout = (segments: EventSegment[]): FinalLayout => {
  const finalLayout: FinalLayout = {
    segmentLayouts: [],
  };

  if (!segments.length) {
    return finalLayout;
  }

  // Sort all segments by start time, then by duration (longer events first to establish columns)
  const sortedSegments = [...segments].sort((a, b) => {
    const startDiff = a.startTime.getTime() - b.startTime.getTime();
    if (startDiff !== 0) return startDiff;
    const durationB = b.endTime.getTime() - b.startTime.getTime();
    const durationA = a.endTime.getTime() - a.startTime.getTime();
    return durationB - durationA;
  });

  const processedSegments = new Set<string>();

  for (const segment of sortedSegments) {
    if (processedSegments.has(segment.id)) {
      continue;
    }

    // Find all overlapping segments for the current segment
    const overlappingGroup = sortedSegments.filter(s => segmentsOverlap(segment, s));
    
    // This will hold the columns of segments for the current overlapping group
    const columns: EventSegment[][] = [];
    
    overlappingGroup.forEach(groupSegment => {
      let placed = false;
      // Find the first column where this segment can fit
      for (const column of columns) {
        if (column.every(s => !segmentsOverlap(groupSegment, s))) {
          column.push(groupSegment);
          placed = true;
          break;
        }
      }
      // If it doesn't fit in any existing column, create a new one
      if (!placed) {
        columns.push([groupSegment]);
      }
    });

    const maxColumns = columns.length;
    const availableWidth = 100 - GUTTER_WIDTH_PERCENT;

    // --- CASCADING LOGIC ---
    // The width is the total available space minus the staggered offsets for all but the first event.
    // Ensure events never exceed the available width to preserve the gutter
    const eventWidth = Math.max(30, availableWidth - ((maxColumns - 1) * CASCADE_STAGGER_PERCENT));

    columns.forEach((column, colIndex) => {
      column.forEach(seg => {
        if (!processedSegments.has(seg.id)) {
          const leftPosition = colIndex * CASCADE_STAGGER_PERCENT;
          // Ensure the event doesn't extend beyond the available width (preserving gutter)
          const maxAllowedWidth = availableWidth - leftPosition;
          const finalWidth = Math.min(eventWidth, maxAllowedWidth);

          finalLayout.segmentLayouts.push({
            segment: seg,
            left: leftPosition,
            width: finalWidth,
            zIndex: 10 + colIndex,
            hasOverlap: maxColumns > 1, // True if there are multiple overlapping events
          });
          processedSegments.add(seg.id);
        }
      });
    });
     // Mark all segments in the processed group so we don't re-calculate them
     overlappingGroup.forEach(s => processedSegments.add(s.id));
  }
  
  return finalLayout;
};

// Deprecated old functions, replaced by calculateLayout

/**
 * @deprecated Use calculateLayout instead. This function will be removed.
 */
export const calculateSegmentLayout = (segments: EventSegment[]): any[] => {
    console.warn("calculateSegmentLayout is deprecated. Use calculateLayout instead.");
    return [];
};


/**
 * @deprecated Use calculateLayout instead. This function will be removed.
 */
export const calculateEventLayout = (events: CalendarEvent[]): any[] => {
    console.warn("calculateEventLayout is deprecated. Use calculateLayout instead.");
    return [];
};

/**
 * Checks if two events overlap in time
 * This might still be useful elsewhere, so we keep it for now.
 */
export const eventsOverlap = (event1: CalendarEvent, event2: CalendarEvent): boolean => {
  const start1 = new Date(event1.start);
  const end1 = new Date(event1.end);
  const start2 = new Date(event2.start);
  const end2 = new Date(event2.end);
  
  return start1 < end2 && start2 < end1;
};

/**
 * A dedicated layout calculator for all-day events, which are laid out horizontally.
 * It determines which events to show and creates a "more" indicator for the rest.
 */
export const calculateAllDayLayout = (
  segments: EventSegment[],
  maxVisible: number = 3,
): { visibleSegments: EventSegment[]; moreCount: number } => {
  if (segments.length <= maxVisible) {
    return { visibleSegments: segments, moreCount: 0 };
  }

  // Sort by start time, then duration
  const sorted = [...segments].sort((a, b) => {
    const startDiff = a.startTime.getTime() - b.startTime.getTime();
    if (startDiff !== 0) return startDiff;
    return (b.endTime.getTime() - b.startTime.getTime()) - (a.endTime.getTime() - a.startTime.getTime());
  });

  const visibleSegments = sorted.slice(0, maxVisible - 1);
  const moreCount = segments.length - visibleSegments.length;

  return { visibleSegments, moreCount };
};

// ... keep other utility functions like groupOverlappingEvents if they might be used elsewhere ...
// For this refactor, we are focusing on replacing the main layout calculation.
// The rest of the file can be cleaned up later.

// Remove the old grouping and layout functions as their logic is now inside calculateLayout
/*
export const groupOverlappingSegments = (segments: EventSegment[]): EventSegment[][] => {
    ...
};
*/
