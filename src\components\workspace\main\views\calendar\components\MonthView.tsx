import React, { useMemo } from 'react';
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameDay, isToday } from 'date-fns';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { PlusIcon } from '@heroicons/react/24/outline';
import { ScrollArea } from '@/components/ui/scroll-area';
import { CalendarEvent } from '@/typings/page';
import { useMaybeRecord } from '@/providers/record';
import { CalendarEventItem } from './CalendarEventItem';
import { NoEvents } from './NoEvents';
import { CalendarSideCard } from './CalendarSideCard';
import { useDroppable } from '@dnd-kit/core';


interface MonthViewProps {
  selectedDate: Date;
  events: CalendarEvent[];
  selectedEvent: string | null;
  setSelectedEvent: (id: string) => void;
  setSelectedDate: (date: Date) => void;
  openAddEventForm: (date: Date) => void;
  canEditData: boolean;
  handleEventClick: (event: CalendarEvent) => void;
  activeDragData: any;
}

const DayCell = ({
  date,
  children,
  onClick,
  isCurrentMonth
}: {
  date: Date;
  children: React.ReactNode;
  onClick: () => void;
  isCurrentMonth: boolean;
}) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `daycell-${format(date, 'yyyy-MM-dd')}`,
    data: {
      date: date,
      type: 'daycell'
    }
  });

  return (
    <div
      ref={setNodeRef}
      onClick={onClick}
      className={cn(
        "border-b border-r border-neutral-300 relative cursor-pointer transition-colors group flex flex-col p-2 min-h-[120px] lg:p-3 lg:min-h-[140px]",
        isCurrentMonth
          ? "bg-white hover:bg-neutral-50"
          : "bg-neutral-100 hover:bg-neutral-200",
        isOver && "bg-blue-50 border-blue-200"
      )}
    >
      {children}
    </div>
  );
};

// New helper function to process events for the entire month
const useMonthEvents = (weeks: Date[][], events: CalendarEvent[]) => {
  return useMemo(() => {
    const positionedEventsByWeek = new Map<number, any[]>();
    const allEventsByDay = new Map<string, any[]>();

    weeks.forEach((week, weekIndex) => {
      const weekStart = week[0];
      const weekEnd = week[6];

      const weekEvents = events.filter(event => {
        const eventStart = new Date(event.start);
        const eventEnd = new Date(event.end);
        return eventStart <= weekEnd && eventEnd >= weekStart;
      });

      const spanningEvents: any[] = [];
      weekEvents.forEach(event => {
        const eventStart = new Date(event.start);
        const eventEnd = new Date(event.end);
        
        const startDayIndex = week.findIndex(day => isSameDay(day, eventStart));
        const endDayIndex = week.findIndex(day => isSameDay(day, eventEnd));

        const eventSpansWeek = startDayIndex !== -1 || endDayIndex !== -1 || (eventStart < weekStart && eventEnd > weekEnd);

        if (eventSpansWeek) {
          const start = startDayIndex !== -1 ? startDayIndex : 0;
          const end = endDayIndex !== -1 ? endDayIndex : 6;
          spanningEvents.push({
            event,
            startDayIndex: start,
            endDayIndex: end,
            colSpan: end - start + 1,
          });
        }
      });
      
      const positioned: any[] = [];
      const rows: any[][] = [];
      const sortedEvents = spanningEvents.sort((a, b) => a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);

      sortedEvents.forEach(event => {
        let assigned = false;
        for (let i = 0; i < rows.length; i++) {
          const row = rows[i];
          if (!row.some(e => event.startDayIndex <= e.endDayIndex && event.endDayIndex >= e.startDayIndex)) {
            row.push(event);
            positioned.push({ ...event, row: i });
            assigned = true;
            break;
          }
        }
        if (!assigned) {
          rows.push([event]);
          positioned.push({ ...event, row: rows.length - 1 });
        }
      });
      positionedEventsByWeek.set(weekIndex, positioned);

      // Track all events that appear in each day cell
      week.forEach((day, dayIndex) => {
        const dayKey = format(day, 'yyyy-MM-dd');
        const dayEvents: any[] = [];
        
        positioned.forEach(pe => {
          // Include events that start on this day
          if (pe.startDayIndex === dayIndex) {
            dayEvents.push({
              ...pe,
              showTitle: true, // Show title on start day
              isDraggable: true // Draggable on start day
            });
          }
          // Include events that continue on this day (multi-day events)
          else if (pe.startDayIndex < dayIndex && pe.endDayIndex >= dayIndex) {
            // Check if this is the first day of a new week AND the event started in a previous week
            const isFirstDayOfWeek = dayIndex === 0;
            const eventStartedInPreviousWeek = pe.startDayIndex < 0; // Event started before this week
            
            dayEvents.push({
              ...pe,
              showTitle: isFirstDayOfWeek, // Show title only on first day of week
              isDraggable: false // Never draggable on continuation days
            });
          }
        });
        
        allEventsByDay.set(dayKey, dayEvents);
      });
    });

    return { positionedEventsByWeek, allEventsByDay };
  }, [weeks, events]);
};

export const MonthView: React.FC<MonthViewProps> = ({
  selectedDate,
  events,
  selectedEvent,
  setSelectedEvent,
  setSelectedDate,
  openAddEventForm,
  canEditData,
  handleEventClick,
  activeDragData,
}) => {
  const maybeRecord = useMaybeRecord();

  const isInRecordTab = !!maybeRecord;

  // Memoize month calculations
  const monthCalculations = useMemo(() => {
    const monthStart = startOfMonth(selectedDate);
    const monthEnd = endOfMonth(selectedDate);
    const startDay = startOfWeek(monthStart, { weekStartsOn: 0 });
    const endDay = endOfWeek(monthEnd, { weekStartsOn: 0 });

    const days = [];
    let day = startDay;
    while (day <= endDay) {
      days.push(day);
      day = addDays(day, 1);
    }

    const weeks = [];
    for (let i = 0; i < days.length; i += 7) {
      weeks.push(days.slice(i, i + 7));
    }

    return { monthStart, monthEnd, startDay, endDay, days, weeks };
  }, [selectedDate]);

  // Memoize month events
  const monthEvents = useMemo(() => 
    events.filter(event => {
      const eventStart = new Date(event.start);
      return eventStart >= monthCalculations.startDay && 
             eventStart <= monthCalculations.endDay;
    }), 
    [events, monthCalculations.startDay, monthCalculations.endDay]
  );

  const { positionedEventsByWeek, allEventsByDay } = useMonthEvents(monthCalculations.weeks, events);

  // Render empty state when no events
  const renderEmptyState = () => (
    <div className="flex flex-col h-full bg-background">
      <div className="grid grid-cols-7 border-b border-neutral-300 bg-white">
        {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (
          <div key={dayName} className={cn(
            "text-center font-semibold text-black",
            "py-2 text-xs"
          )}>
            {dayName.substring(0, 3)}
          </div>
        ))}
      </div>

      <NoEvents
        title="No events this month"
        message={`${format(selectedDate, 'MMMM yyyy')} is completely free. Start planning your month!`}
        showCreateButton={canEditData}
        onCreate={() => openAddEventForm(selectedDate)}
      />
    </div>
  );

  // Render day cell content
  const renderDayCellContent = (day: Date, dayEvents: any[]) => {
    const isCurrentMonth = day.getMonth() === selectedDate.getMonth();
    const isCurrentDay = isToday(day);
    const MAX_VISIBLE_EVENTS = 4;
    const ROW_HEIGHT = 28;

    // Get all events for this specific day (including multi-day events that continue here)
    const dayKey = format(day, 'yyyy-MM-dd');
    const allDayEvents = allEventsByDay.get(dayKey) || [];
    
    // Sort events by row and limit to visible events
    const sortedEvents = allDayEvents.sort((a, b) => a.row - b.row);
    const visibleEvents = sortedEvents.slice(0, MAX_VISIBLE_EVENTS);
    const hasMore = sortedEvents.length > MAX_VISIBLE_EVENTS;

    // FIXED HEIGHT CALCULATION: Always use 5 rows (4 events + 1 "+ more" row)
    // This prevents cell expansion regardless of how many events there are
    const containerHeight = (MAX_VISIBLE_EVENTS + 1) * ROW_HEIGHT; // 5 * 28 = 140px

    return (
      <>
        <div className="flex items-center justify-between mb-2">
          <span className={cn(
            "inline-flex items-center justify-center rounded-full font-semibold text-xs w-6 h-6",
            isCurrentDay ? "bg-black text-white" : isCurrentMonth ? "text-black hover:bg-neutral-100" : "text-neutral-400"
          )}>
            {format(day, 'd')}
          </span>
          {canEditData && isCurrentMonth && (
            <Button
              variant="ghost"
              className="rounded-full h-6 w-6 p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-neutral-100 hidden lg:inline-flex"
              onClick={(e) => {
                e.stopPropagation();
                setTimeout(() => openAddEventForm(day), 150);
              }}
            >
              <PlusIcon className="h-3 w-3 text-black" />
            </Button>
          )}
        </div>

        <div className="relative" style={{ height: `${containerHeight}px` }}>
          {visibleEvents.map(pe => (
            <div
              key={pe.event.id}
              className="absolute"
              style={{
                top: `${pe.row * ROW_HEIGHT}px`,
                left: '2px',
                width: pe.colSpan > 1 
                  ? `calc(${pe.colSpan * 100}% + ${(pe.colSpan - 1) * 19}px)`
                  : 'calc(100% - 4px)',
                zIndex: 10 + pe.row,
              }}
            >
              <CalendarEventItem
                event={pe.event}
                view="month"
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedEvent(pe.event.id);
                  handleEventClick(pe.event);
                }}
                isDragging={activeDragData?.payload?.id === pe.event.id}
                showTitle={pe.showTitle}
                isDraggable={pe.isDraggable}
              />
            </div>
          ))}

          {hasMore && (
            <div 
              className="text-black hover:text-black font-medium text-xs cursor-pointer"
              style={{
                position: 'absolute',
                top: `${MAX_VISIBLE_EVENTS * ROW_HEIGHT}px`,
                left: '2px',
              }}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedDate(day);
              }}
            >
              + {sortedEvents.length - MAX_VISIBLE_EVENTS} more
            </div>
          )}
        </div>
      </>
    );
  };

  // Render main view
  return (
    <div className="h-full bg-background flex flex-col lg:flex-row">
      <div className="flex-1 flex flex-col min-h-0">
        {/* Day Headers */}
        <div className="grid grid-cols-7 border-b border-neutral-300 sticky top-0 z-10 bg-white">
          {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (
            <div key={dayName} className={cn(
              "text-center font-semibold text-black",
              "py-2 text-xs"
            )}>
              {dayName.substring(0, 3)}
            </div>
          ))}
        </div>

        {/* Month Grid */}
        <ScrollArea className="flex-1">
          <div className="grid grid-cols-7 border-neutral-200 border-b">
            {monthCalculations.weeks.map((week, weekIndex) =>
              week.map((day, dayIndex) => {
                const isCurrentMonth = day.getMonth() === selectedDate.getMonth();

                return (
                  <DayCell
                    key={`${weekIndex}-${dayIndex}`}
                    date={day}
                    isCurrentMonth={isCurrentMonth}
                    onClick={() => setSelectedDate(day)}
                  >
                    {renderDayCellContent(day, [])}
                  </DayCell>
                );
              }),
            )}
          </div>
        </ScrollArea>
      </div>

      <CalendarSideCard
        selectedDate={selectedDate}
        events={events}
        selectedEvent={selectedEvent}
        setSelectedEvent={setSelectedEvent}
        handleEventClick={handleEventClick}
      />
    </div>
  );
};

function isMultiDay(event: CalendarEvent): boolean {
  return !isSameDay(new Date(event.start), new Date(event.end));
}