import React, { useMemo } from 'react';
import { format, addDays, startOfWeek, endOfWeek, isSameDay } from 'date-fns';
import { cn } from '@/lib/utils';
import { CalendarEvent } from '@/typings/page';
import { EventSegment } from '@/utils/multiDayEventUtils';
import { CalendarEventSegment } from './CalendarEventSegment';
import { calculateAllDayLayout } from '@/utils/eventCollisionUtils';
import { useDroppable } from '@dnd-kit/core';

interface AllDayRowProps {
  selectedDate: Date;
  segments: EventSegment[];
  selectedEvent: string | null;
  setSelectedEvent: (id: string) => void;
  handleEventClick: (event: CalendarEvent) => void;
  canEditData: boolean;
  openAddEventForm: (date: Date) => void;
  view?: 'day' | 'week';
  activeDragData?: any;
}

interface SpanningEvent {
  segment: EventSegment;
  startDayIndex: number;
  endDayIndex: number;
  colSpan: number;
  isEndOfEvent: boolean;
}

  selectedDate,
  segments,
  selectedEvent,
  setSelectedEvent,
  handleEventClick,
  canEditData,
  openAddEventForm,
  view,
  activeDragData
}) => {
  const droppableId = `allday-droppable-${view}-${format(startOfWeek(selectedDate), 'yyyy-MM-dd')}`;
  const { setNodeRef, isOver } = useDroppable({ id: droppableId });

  if (segments.length === 0 && !activeDragData) {
    return null;
  }

  const renderDayView = () => (
    <div className="border-b border-neutral-300 bg-white">
      <div className="flex">
        <div className="sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-neutral-500">
          All-day
        </div>
        <div ref={setNodeRef} className={cn("flex-1 relative p-2 space-y-1", isOver && "bg-blue-50")}>
          {segments.slice(0, 3).map((segment) => (
            <CalendarEventSegment
              key={segment.id}
              segment={segment}
              style={{ height: '24px', width: '100%' }}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedEvent(segment.originalEventId);
                handleEventClick(segment.originalEvent);
              }}
              view="day"
              isDragging={activeDragData?.payload?.id === segment.id}
            />
          ))}
          {segments.length > 3 && (
            <div className="text-xs text-neutral-600 font-medium cursor-pointer hover:text-neutral-800">
              + {segments.length - 3} more
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderWeekView = () => {
    const weekDays = Array.from({ length: 7 }, (_, i) => addDays(startOfWeek(selectedDate, { weekStartsOn: 0 }), i));
    const { visibleSegments, moreCount } = calculateAllDayLayout(segments, 3);

    return (
      <div className="border-b border-neutral-300 bg-white">
        <div className="flex">
          <div className="sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-center justify-end pr-4 pt-2 text-xs font-medium text-neutral-500">
            All-day
          </div>
          <div ref={setNodeRef} className={cn("flex-1 relative p-2 grid grid-cols-7 gap-1", isOver && "bg-blue-50")}>
            {weekDays.map((day, dayIndex) => (
              <div key={dayIndex} className="relative h-full">
                {visibleSegments
                  .filter(segment => isSameDay(segment.date, day))
                  .map(segment => (
                    <CalendarEventSegment
                      key={segment.id}
                      segment={segment}
                      style={{ height: '24px', width: '100%' }}
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedEvent(segment.originalEventId);
                        handleEventClick(segment.originalEvent);
                      }}
                      view="week"
                      isDragging={activeDragData?.payload?.id === segment.id}
                    />
                  ))}
                {moreCount > 0 && dayIndex === 0 && (
                  <div className="text-xs text-neutral-600 font-medium cursor-pointer hover:text-neutral-800 mt-1">
                    + {moreCount} more
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return view === 'day' ? renderDayView() : renderWeekView();
}; 