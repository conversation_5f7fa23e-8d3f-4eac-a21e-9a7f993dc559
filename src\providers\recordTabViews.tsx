"use client";

import React, { createContext, useContext, useState, useCallback, useMemo, PropsWithChildren } from 'react'
import { DbRecordFilter, DbRecordSort, Match } from 'opendb-app-db-utils/lib/typings/db'

interface RecordTabViewsContextProps {
    filter: DbRecordFilter
    setFilter: (filter: DbRecordFilter) => void
    sorts: DbRecordSort[]
    setSorts: (sorts: DbRecordSort[]) => void
    search: string
    setSearch: (search: string) => void
    selectedIds: string[]
    setSelectedIds: (ids: string[]) => void
    clearState: () => void
}

export const RecordTabViewsContext = createContext<RecordTabViewsContextProps | null>(null)

export const RecordTabViewsProvider = ({ children }: PropsWithChildren) => {
    const [filter, setFilter] = useState<DbRecordFilter>({ match: Match.All, conditions: [] })
    const [sorts, setSorts] = useState<DbRecordSort[]>([])
    const [search, setSearch] = useState('')
    const [selectedIds, setSelectedIds] = useState<string[]>([])

    const clearState = useCallback(() => {
        setFilter({ match: Match.All, conditions: [] })
        setSorts([])
        setSearch('')
        setSelectedIds([])
    }, [])

    const contextValue = useMemo(() => ({
        filter,
        setFilter,
        sorts,
        setSorts,
        search,
        setSearch,
        selectedIds,
        setSelectedIds,
        clearState,
    }), [filter, sorts, search, selectedIds, clearState])

    return (
        <RecordTabViewsContext.Provider value={contextValue}>
            {children}
        </RecordTabViewsContext.Provider>
    )
}

export const useRecordTabViews = () => {
    const context = useContext(RecordTabViewsContext)
    if (!context) {
        throw new Error('useRecordTabViews must be used within a RecordTabViewsProvider')
    }
    return context
} 