import {TableViewDefinition, ViewDefinition} from "opendb-app-db-utils/lib/typings/view";
import React, {useEffect, useRef, useState} from "react";
import "./table.css"
import {useWorkspace} from "@/providers/workspace";
import 'react-data-grid/lib/styles.css';
import DataGrid, {Column, DataGridHandle} from "react-data-grid";
import {TextAreaEditor, TextEditor, TextRenderer, UUIDRenderer} from "@/components/workspace/main/views/table/renderer/fields/text";
import {Header<PERSON>enderer} from "@/components/workspace/main/views/table/renderer/common/header";
import {DatabaseColumn, DatabaseFieldDataType, DbRecordFilter, DbRecordSort, LinkedColumn, MagicColumn, Match, ProcessedDbRecord, Sort} from "opendb-app-db-utils/lib/typings/db";
import {CheckboxRenderer} from "@/components/workspace/main/views/table/renderer/fields/checkbox";
import {DateEditor, DateRenderer} from "@/components/workspace/main/views/table/renderer/fields/date";
import {members<PERSON><PERSON><PERSON><PERSON><PERSON>, PersonEditor, PersonRenderer} from "@/components/workspace/main/views/table/renderer/fields/person";
import {FileEditor, FileRenderer} from "@/components/workspace/main/views/table/renderer/fields/files";
import {DatabaseConstants, getNewColumnProps} from "@/components/workspace/main/views/table/renderer/common/addColumn";
import {getSelectColumnProps} from "@/components/workspace/main/views/table/renderer/common/selectRow";
import {Database, Record} from "@/typings/database";
import {PageLoader} from "@/components/custom-ui/loader";
import {AIRenderer} from "@/components/workspace/main/views/table/renderer/fields/ai";
import {SelectEditor, SelectRenderer} from "@/components/workspace/main/views/table/renderer/fields/select";
import {LinkedEditor, LinkedRenderer} from "@/components/workspace/main/views/table/renderer/fields/linked";
import { useViews, useViewFiltering, useViewSelection } from "@/providers/views";
import {filterRecords, LinkedDatabases, RecordResponse, sortRecords, transformRawRecords} from "opendb-app-db-utils/lib/utils/db";
import {Person} from "opendb-app-db-utils/lib/typings/common";
import {DatabaseRecordStore, DatabaseRecordStoreItem} from "@/typings/utilities";
import {MyWorkspaceMember} from "@/typings/workspace";
import {View} from "@/typings/page";
import {ContentLocked} from "@/components/workspace/main/views/common/contentLocked";
import {useMaybeShared} from "@/providers/shared";
import {usePage} from "@/providers/page";
import {SummarizeRenderer} from "@/components/workspace/main/views/table/renderer/fields/summarize";
import {ExclamationCircleIcon} from "@heroicons/react/24/outline";
import {Button} from "@/components/ui/button";
import {ImportRecords} from "@/components/workspace/main/database/importRecords";
import {useBroadcast} from "@/providers/broadcast";
import useForceRender from "@/components/custom-ui/forceRender";
import {useMaybeTemplate} from "@/providers/template";
// DerivedFormulaEditor is used in HeaderRenderer, imported there directly
import { ButtonGroupRenderer } from "@/components/workspace/main/views/table/renderer/fields/buttonGroup";
import {ScannableCodeRenderer} from "@/components/workspace/main/views/table/renderer/fields/scannableCode";
import {useMaybeRecord} from "@/providers/record";

// import {GridRowRender} from "@/components/workspace/main/views/table/renderer/common/gridRender";


export interface ViewRenderProps {
    view: View
    definition: ViewDefinition
    // updateDefinition?: (update: Partial<ViewDefinition>) => void
    // createColumn?: () => void
    // updateColumn?: () => void
    // deleteColumn?: () => void
    // updateRecord?: () => void
    // addRecords?: () => void
    // deleteRecords?: () => void
}

export interface TableViewRenderProps extends ViewRenderProps {
    definition: TableViewDefinition
}

export interface RGDMeta {
    column: DatabaseColumn
    databaseId: string
    sortable?: boolean
    triggerEdit?: boolean
    headerLocked?: boolean
    contentLocked?: boolean
}

export interface DataViewRow {
    id: string
    record: Record
    processedRecord: ProcessedDbRecord
    updatedAt: string
}

export const TableView = (props: TableViewRenderProps) => {
    const {databaseStore, databaseErrorStore, members, workspace} = useWorkspace()
    const {definition} = props
    const {cache} = useViews()
    const {filter, sorts, search} = useViewFiltering()
    const {selectedIds, setSelectedIds} = useViewSelection()
    const {accessLevel} = usePage()
    const {registerListener} = useBroadcast()

    const maybeShared = useMaybeShared()
    const maybeRecord = useMaybeRecord()

    definition.filter = definition.filter || {conditions: [], match: Match.All}
    definition.sorts = definition.sorts || []

    const databaseId = definition.databaseId
    const database = databaseStore[definition.databaseId]

    const isPublishedView = !!maybeShared
    const editable = !definition.lockContent && !isPublishedView && !!accessLevel

    const maybeTemplate = useMaybeTemplate()

    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable)
    let canEditData: boolean = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable)

    const gridRef = useRef<DataGridHandle | null>(null);
    const containerEle = useRef<HTMLDivElement | null>(null);

    let scrollToRowId = -1

    const editColIdRef = useRef('')
    let editColumnTrigger = false

    const getColDefs = () => {
        const columns: Column<any, any>[] = []

        // const autoEdit: {
        //     databaseId: string,
        //     columnId: string
        // } | null = cache.getCache(DatabaseConstants.NewlyCreatedColumnKey)

        if (!database) return columns
        const dbDefinition = database.database.definition
        if (!dbDefinition) return columns

        let {columnsOrder, columnPropsMap} = definition
        columnsOrder = Array.isArray(columnsOrder) ? columnsOrder : []
        columnPropsMap = columnPropsMap || {}

        for (const key of dbDefinition.columnIds) {
            if (!columnsOrder.includes(key)) columnsOrder.push(key)
            if (!columnPropsMap[key]) columnPropsMap[key] = {}
        }
        for (const id of columnsOrder) {
            // {key: dataPoint.key, name: dataPoint.key, field: dataPoint.key}
            const dbCol = dbDefinition.columnsMap[id]
            if (!dbCol) continue
            if (columnPropsMap[id].isHidden) continue

            const triggerEdit = editColIdRef.current === dbCol.id
            // const triggerEdit = !!(autoEdit && autoEdit.databaseId === definition.databaseId && autoEdit.columnId === dbCol.id)
            const __meta__: RGDMeta = {
                databaseId: definition.databaseId,
                column: dbCol,
                triggerEdit: triggerEdit,
                headerLocked: !canEditStructure,
                contentLocked: isPublishedView || definition.lockContent,
            }
            // if (triggerEdit) scrollToColId = columns.length
            if (triggerEdit) editColumnTrigger = true
            const column = {
                key: id,
                name: dbCol.title,
                renderEditCell: TextEditor,
                renderCell: TextRenderer,
                renderHeaderCell: HeaderRenderer,
                width: 200,
                editable: true,
                __meta__
                // fieldType: dbCol.type
            }

            switch (dbCol.type) {
                case DatabaseFieldDataType.AI:
                    column.renderCell = AIRenderer
                    column.renderEditCell = TextAreaEditor
                    // column.editable = true
                    break;
                case DatabaseFieldDataType.UUID:
                    column.renderCell = UUIDRenderer
                    // @ts-ignore
                    delete column.renderEditCell
                    break
                case DatabaseFieldDataType.Number:
                case DatabaseFieldDataType.Text:
                case DatabaseFieldDataType.Derived:
                    if (dbCol.type === DatabaseFieldDataType.Text && dbCol.isLong) column.renderEditCell = TextAreaEditor
                    if (dbCol.type === DatabaseFieldDataType.Derived) {
                        column.renderEditCell = TextAreaEditor
                    }
                    // @ts-ignore
                    // column.editable = true
                    break
                case DatabaseFieldDataType.Linked:
                    column.renderCell = LinkedRenderer
                    column.renderEditCell = LinkedEditor
                    column.editable = !(!dbCol.databaseId || databaseErrorStore[dbCol.databaseId] || !databaseStore[dbCol.databaseId]);
                    break;
                case DatabaseFieldDataType.Summarize:
                    column.renderCell = SummarizeRenderer
                    column.editable = false
                    break;
                case DatabaseFieldDataType.Select:
                    column.renderCell = SelectRenderer
                    column.renderEditCell = SelectEditor
                    // column.editable = true
                    break;
                case DatabaseFieldDataType.Checkbox:
                    column.renderCell = CheckboxRenderer
                    // column.editable = true
                    // @ts-ignore
                    delete column.renderEditCell
                    break;
                case DatabaseFieldDataType.Date:
                    column.renderCell = DateRenderer
                    column.renderEditCell = DateEditor
                    break;
                case DatabaseFieldDataType.CreatedAt:
                case DatabaseFieldDataType.UpdatedAt:
                    column.renderCell = DateRenderer
                    // @ts-ignore
                    delete column.renderEditCell
                    break;
                case DatabaseFieldDataType.Person:
                    column.renderCell = PersonRenderer
                    column.renderEditCell = PersonEditor
                    break;
                case DatabaseFieldDataType.CreatedBy:
                case DatabaseFieldDataType.UpdatedBy:
                    column.renderCell = PersonRenderer
                    // @ts-ignore
                    delete column.renderEditCell
                    break
                case DatabaseFieldDataType.Files:
                    column.renderCell = FileRenderer
                    column.renderEditCell = FileEditor
                    column.editable = true
                    break;
                case DatabaseFieldDataType.ScannableCode:
                    column.renderCell = ScannableCodeRenderer
                    break
                case DatabaseFieldDataType.ButtonGroup:
                    column.renderCell = ButtonGroupRenderer;
                        // ButtonGroup is read-only, so we don't need an editor
                    column.editable = false;
                    break;

                default:
                    continue
            }
            column.editable = column.editable && canEditData
            columns.push(column)
        }

        if (canEditStructure) columns.push(getNewColumnProps(database.database.id))
       // columns.unshift(getSelectColumnProps(columns.length, canEditStructure))

        const isInRecordContext = !!maybeRecord
        columns.unshift(getSelectColumnProps(columns.length, canEditStructure, isInRecordContext))

        return columns
    }

    const colIds: string[] | null = cache.getCache(DatabaseConstants.NewlyCreatedRecordsKey)
    const createdColIds: string[] = colIds && Array.isArray(colIds) ? colIds : []

    const cols = getColDefs()

    const getProcessedRows = (): DataViewRow[] => {
        if (!database) return []

        const sortOptions: DbRecordSort[] = []
        if (sorts.length > 0) {
            sortOptions.push(...sorts)
        } else if (definition.sorts.length > 0) {
            sortOptions.push(...definition.sorts)
        }
        if (sortOptions.length === 0) sortOptions.push({columnId: MagicColumn.CreatedAt, order: Sort.Asc})

        const {rows} = filterAndSortRecords(
            database,
            members,
            databaseStore,
            definition.filter,
            filter,
            sortOptions,
            workspace.workspaceMember.userId,
            '',
            maybeRecord?.recordInfo.record.id,
            createdColIds
        )
        return rows
    }

    const filteredRows = getProcessedRows()

    const rows = searchFilteredRecords(search, filteredRows)
    console.log(rows)

    function rowKeyGetter(row: any) {
        return row.id;
    }

    // console.log({scrollToColId, scrollToRowId})
    // console.log({sorts, filter, rows, createdAts: rows.map(r => r.record.createdAt)})

    const {forceRender} = useForceRender();

    useEffect(() => {
        if (!gridRef.current) return
        // if (scrollToColId > -1) {
        //     gridRef.current?.scrollToCell({idx: scrollToColId + 2});
        //
        //     // console.log("Clearing New Col Key", {scrollToColId})
        //     // cache.clearCache(DatabaseConstants.NewlyCreatedColumnKey)
        // }
        if (scrollToRowId > -1) {
            gridRef.current?.scrollToCell({rowIdx: scrollToRowId + 1})

            // console.log("Clearing New Row Key", {scrollToRowId})
            // cache.clearCache(DatabaseConstants.NewlyCreatedRecordsKey)
        }
        if (editColIdRef.current && editColumnTrigger) {
            // setEditColumnId("")
            editColIdRef.current = ''
            // forceRenderRef.current()
        }
        // }, [cache, scrollToColId, scrollToRowId]);
    }, [cache, editColumnTrigger, scrollToRowId]);
    // }, [cache, editColumnId, editColumnTrigger, scrollToRowId]);
    // }, [cache, scrollToColId, scrollToRowId]);

    // const setEditColIdRef = useRef(setEditColumnId)


    // setEditColIdRef.current = setEditColumnId

    // console.log("Render", {editColumnId: editColIdRef.current, editColumnTrigger, cols})

    const forceRenderRef = useRef(forceRender)

    useEffect(() => {
        const listener = (data: { columnId: string }) => {
            const {columnId} = data
            // setEditColIdRef.current(columnId)
            // setEditColIdRef.current(columnId)
            // setEditColumnId(columnId)

            editColIdRef.current = columnId
            forceRenderRef.current()

            console.log("New column listener called:", {columnId})
        };
        const unregister = registerListener(`d:${databaseId}`, DatabaseConstants.NewlyCreatedColumnKey, listener);

        return () => {
            unregister();
        };
    }, [databaseId, registerListener]);

    // useEffect(() => {
    //     const createdColIds: string[] = colIds && Array.isArray(colIds) ? colIds : []
    //     if (createdColIds.length > 0) {
    //         setTimeout(() => {
    //             cache.clearCache(DatabaseConstants.NewlyCreatedRecordsKey)
    //         }, 3000)
    //     }
    // }, [colIds, cache])

    return (
        <>
            {!definition && <PageLoader size='full' error="Unable to load database"/>}

            {definition && <div className='size-full flex flex-col'>
                {!isPublishedView && definition.lockContent && <>
                    <ContentLocked/>
                </>}
                <div className="w-full flex-1 overflow-hidden table-view bg-white relative" ref={containerEle}>
                    <DataGrid
                        ref={gridRef}
                        columns={cols}
                        rows={rows}
                        // rows={demoDatabase.rows}
                        rowHeight={40}
                        headerRowHeight={40}
                        summaryRowHeight={40}
                        renderers={{
                            noRowsFallback: <EmptyRowsRenderer
                                width={containerEle.current?.clientWidth}
                                database={database.database}/>
                        }}
                        className="w-full h-full rdg-light"
                        // defaultColumnOptions={{
                        //     sortable: true,
                        //     resizable: true
                        // }}
                        rowKeyGetter={rowKeyGetter}
                        selectedRows={new Set(selectedIds)}
                        onSelectedRowsChange={ids => setSelectedIds(Array.from(ids) as string[])}
                        rowClass={(row, index) => {
                            // row.id.includes('7') || index === 0 ? highlightClassname : undefined
                            return createdColIds.includes(row.id) ? 'bg-neutral-100 animate-pulse' : undefined
                        }}
                    />
                </div>
            </div>}

        </>
    )
}

const EmptyRowsRenderer = ({database, width}: { database: Database, width?: number }) => {

    const {createRecords} = useViews()
    const [open, setOpen] = useState(false)

    const addRow = async () => {
        const rS = await createRecords(database.id, [{}])
    }


    const content = <div style={{textAlign: 'center', width: width, minWidth: width, overflow: "hidden"}} className='content-center sticky left-0 top-2 w-full'>
        <div className='p-8 flex flex-col items-center min-h-72'>
            <div className='my-4'>
                <ExclamationCircleIcon width={48} height={48} className='inline'/>
            </div>
            <span className='text-sm font-medium'>Database has no records</span>
            <div className='my-4 flex justify-center items-center gap-2'>
                <Button variant='ghost' className='text-xs h-8 font-semibold' onClick={addRow}>
                    Add Row
                </Button>
                <Button className='text-xs px-4 h-8 text-center items-center font-semibold'
                        onClick={() => setOpen(true)}>Import from file</Button>
            </div>
        </div>
    </div>

    return <>
        {open && <ImportRecords database={database} close={() => setOpen(false)}/>}

        {width && width > 0 ? <>
            {content}
        </> : <>
             <div style={{textAlign: 'center', gridColumn: '1/-1', overflow: "hidden"}} className='content-center'>
                 {content}
             </div>
         </>}


    </>
}

export const filterAndSortRecords = (
    database: DatabaseRecordStoreItem, members: MyWorkspaceMember[], databaseStore: DatabaseRecordStore,
    definitionFilter: DbRecordFilter,
    filter: DbRecordFilter,
    sortOptions: DbRecordSort[],
    currentUserId: string,
    currentObjectId: string = '',
    currentRecordId?: string,
    newlyCreatedRecordIds?: string[]
) => {
    const rows: DataViewRow[] = []
    if (!database) return {rows}
    const processedFilter = { ...filter }
    const processedDefinitionFilter = { ...definitionFilter }
    const isCurrentRecordNewlyCreated = currentRecordId && newlyCreatedRecordIds && newlyCreatedRecordIds.includes(currentRecordId)
    
    // Process current_record magic values in contextual filters
    if (currentRecordId && processedFilter.conditions && !isCurrentRecordNewlyCreated) {
        processedFilter.conditions = processedFilter.conditions.map(condition => {
            // Handle array values (linked columns)
            if (Array.isArray(condition.value) && condition.value.includes('current_record')) {
                return {
                    ...condition,
                    value: condition.value.map(v => v === 'current_record' ? currentRecordId : v)
                }
            }
            // Handle string values (UUID columns)
            if (typeof condition.value === 'string' && condition.value === 'current_record') {
                return {
                    ...condition,
                    value: currentRecordId
                }
            }
            return condition
        })
    } else if (isCurrentRecordNewlyCreated && processedFilter.conditions) {
        processedFilter.conditions = processedFilter.conditions.filter(condition => {
            // Handle array values (linked columns)
            if (Array.isArray(condition.value) && condition.value.includes('current_record')) {
                return false 
            }
            // Handle string values (UUID columns)
            if (typeof condition.value === 'string' && condition.value === 'current_record') {
                return false 
            }
            return true
        })
    }
    
    // Process current_record magic values in definition filters (default filters)
    if (currentRecordId && processedDefinitionFilter.conditions && !isCurrentRecordNewlyCreated) {
        processedDefinitionFilter.conditions = processedDefinitionFilter.conditions.map(condition => {
            // Handle array values (linked columns)
            if (Array.isArray(condition.value) && condition.value.includes('current_record')) {
                return {
                    ...condition,
                    value: condition.value.map(v => v === 'current_record' ? currentRecordId : v)
                }
            }
            // Handle string values (UUID columns)
            if (typeof condition.value === 'string' && condition.value === 'current_record') {
                return {
                    ...condition,
                    value: currentRecordId
                }
            }
            return condition
        })
    } else if (isCurrentRecordNewlyCreated && processedDefinitionFilter.conditions) {
        processedDefinitionFilter.conditions = processedDefinitionFilter.conditions.filter(condition => {
            // Handle array values (linked columns)
            if (Array.isArray(condition.value) && condition.value.includes('current_record')) {
                return false 
            }
            // Handle string values (UUID columns)
            if (typeof condition.value === 'string' && condition.value === 'current_record') {
                return false 
            }
            return true
        })
    }
    
    const linkedDatabaseId = database ? Object.values(database.database.definition.columnsMap)
        .filter(c => c.type === DatabaseFieldDataType.Linked && c.databaseId)
        .map(c => (c as LinkedColumn).databaseId) : []

    const linkedDatabases: LinkedDatabases = {}

    for (const id of linkedDatabaseId) {
        const db = databaseStore[id]
        if (db) {
            linkedDatabases[id] = {
                id,
                definition: db.database.definition,
                srcPackageName: db.database.srcPackageName,
                recordsMap: {}
            }
            for (let r of Object.values(db.recordsIdMap)) {
                linkedDatabases[id].recordsMap[r.record.id] = r.record
            }
        }
    }
    const persons: Person[] = membersToPersons(members)
    const records = Object.values(database.recordsIdMap).map(r => r.record)
    const processedRecords = transformRawRecords(
        // const processedRecords = debugTransformRawRecords(
        database.database.definition,
        records,
        persons,
        linkedDatabases
    )
    let filtered: RecordResponse = {processedRecords, records}
    if (processedDefinitionFilter && processedDefinitionFilter.conditions.length > 0) {
        filtered = filterRecords(filtered.records, filtered.processedRecords, processedDefinitionFilter, database.database.definition, currentUserId, currentObjectId)
    }
    if (processedFilter && processedFilter.conditions.length > 0) {
        filtered = filterRecords(filtered.records, filtered.processedRecords, processedFilter, database.database.definition, currentUserId, currentObjectId)
    }
    if (sortOptions.length === 0) sortOptions.push({columnId: MagicColumn.CreatedAt, order: Sort.Asc})

    const sorted = sortRecords(filtered.records, filtered.processedRecords, sortOptions, database.database.definition)

    for (let i = 0; i < sorted.records.length; i++) {
        const record = sorted.records[i]
        const processedRecord = sorted.processedRecords[i]

        const row: DataViewRow = {
            id: record.id,
            record: database.recordsIdMap[record.id].record,
            updatedAt: new Date(record.updatedAt).toISOString(),
            processedRecord
        }
        rows.push(row)
    }

    return {rows}
}

export const searchFilteredRecords = (search: string, filteredRows: DataViewRow[]) => {
    if (!search || !search.trim()) return filteredRows
    return filteredRows.filter(r => r.processedRecord.valuesText.toLowerCase().includes(search.trim().toLowerCase()))
}