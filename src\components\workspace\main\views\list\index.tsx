"use client";

import React, { useRef, useEffect } from "react";
import {useWorkspace} from "@/providers/workspace";
import {DatabaseColumn, DatabaseFieldDataType, DbRecordSort, MagicColumn, Match, Sort} from "opendb-app-db-utils/lib/typings/db";
import { useViews, useViewFiltering, useViewSelection } from "@/providers/views";
import {filterAndSortRecords, searchFilteredRecords, DataViewRow, RGDMeta} from "@/components/workspace/main/views/table";
import {ListViewDefinition, ViewDefinition} from "opendb-app-db-utils/lib/typings/view";
import {Text<PERSON><PERSON><PERSON>, UUIDRenderer} from "@/components/workspace/main/views/table/renderer/fields/text";
import {CheckboxRenderer} from "@/components/workspace/main/views/table/renderer/fields/checkbox";
import {DateRenderer} from "@/components/workspace/main/views/table/renderer/fields/date";
import {<PERSON><PERSON><PERSON><PERSON>} from "@/components/workspace/main/views/table/renderer/fields/person";
import {File<PERSON><PERSON><PERSON>} from "@/components/workspace/main/views/table/renderer/fields/files";
import {AIRenderer} from "@/components/workspace/main/views/table/renderer/fields/ai";
import {SelectRenderer} from "@/components/workspace/main/views/table/renderer/fields/select";
import {LinkedRenderer} from "@/components/workspace/main/views/table/renderer/fields/linked";
import {usePage} from "@/providers/page";
import {SummarizeRenderer} from "@/components/workspace/main/views/table/renderer/fields/summarize";
import {ButtonGroupRenderer} from "@/components/workspace/main/views/table/renderer/fields/buttonGroup";
import {ScannableCodeRenderer} from "@/components/workspace/main/views/table/renderer/fields/scannableCode";
import {useMaybeRecord} from "@/providers/record";
import {useMaybeShared} from "@/providers/shared";
import {useMaybeTemplate} from "@/providers/template";
import {View} from "@/typings/page";
import {ScrollArea} from "@/components/ui/scroll-area";
import {getDatabaseTitleCol, getRecordTitle} from "@/components/workspace/main/views/form/components/element/linked";
import {timeAgo} from "@/utils/timeAgo";
import {useRouter, usePathname} from "next/navigation";
import {useStackedPeek} from "@/providers/stackedPeek";
import "./list.css";

export interface ViewRenderProps {
    view: View
    definition: ViewDefinition
}

export interface ListViewRenderProps extends ViewRenderProps {
    definition: ListViewDefinition
}

const ListFieldRenderer = ({ 
    field, 
    row, 
    databaseId,
    isPublishedView,
    lockContent 
}: { 
    field: DatabaseColumn, 
    row: DataViewRow,
    databaseId: string,
    isPublishedView: boolean,
    lockContent: boolean
}) => {
    const meta: RGDMeta = {
        databaseId: databaseId,
        column: field,
        triggerEdit: false,
        headerLocked: true,
        contentLocked: true,
    };

    const renderProps = {
        column: {
            key: field.id,
            __meta__: meta,
            idx: 0,
            name: field.title,
            frozen: false,
            resizable: false,
            sortable: false,
            width: 150,
            minWidth: 50,
            maxWidth: undefined,
            cellClass: undefined,
            headerCellClass: undefined,
            editable: false
        },
        row: row,
        rowIdx: 0,
        tabIndex: -1,
        onRowChange: () => {},
        isCellSelected: false,
        selectCell: () => {},
        isRowSelected: false
    };

    let RendererComponent = TextRenderer;
    
    switch (field.type) {
        case DatabaseFieldDataType.AI:
            RendererComponent = AIRenderer;
            break;
        case DatabaseFieldDataType.UUID:
            RendererComponent = UUIDRenderer;
            break;
        case DatabaseFieldDataType.Number:
        case DatabaseFieldDataType.Text:
        case DatabaseFieldDataType.Derived:
            RendererComponent = TextRenderer;
            break;
        case DatabaseFieldDataType.Linked:
            RendererComponent = LinkedRenderer;
            break;
        case DatabaseFieldDataType.Summarize:
            RendererComponent = SummarizeRenderer;
            break;
        case DatabaseFieldDataType.Select:
            RendererComponent = SelectRenderer;
            break;
        case DatabaseFieldDataType.Checkbox:
            RendererComponent = CheckboxRenderer;
            break;
        case DatabaseFieldDataType.Date:
        case DatabaseFieldDataType.CreatedAt:
        case DatabaseFieldDataType.UpdatedAt:
            RendererComponent = DateRenderer;
            break;
        case DatabaseFieldDataType.Person:
        case DatabaseFieldDataType.CreatedBy:
        case DatabaseFieldDataType.UpdatedBy:
            RendererComponent = PersonRenderer;
            break;
        case DatabaseFieldDataType.Files:
            RendererComponent = FileRenderer;
            break;
        case DatabaseFieldDataType.ScannableCode:
            RendererComponent = ScannableCodeRenderer;
            break;
        case DatabaseFieldDataType.ButtonGroup:
            RendererComponent = ButtonGroupRenderer;
            break;
        default:
            RendererComponent = TextRenderer;
    }
    
    // @ts-ignore
    return <RendererComponent {...renderProps} />;
};

export const ListView = (props: ListViewRenderProps) => {
    const {databaseStore, databaseErrorStore, members, workspace, url} = useWorkspace()
    const {definition} = props
    const {cache, setPeekRecordId} = useViews()
    const {filter, sorts, search} = useViewFiltering()
    const {selectedIds, setSelectedIds} = useViewSelection()
    const {accessLevel} = usePage()

    const maybeShared = useMaybeShared()
    const maybeRecord = useMaybeRecord()
    const router = useRouter()
    const pathname = usePathname()
    const {openRecord} = useStackedPeek()

    const contentScrollRef = useRef<HTMLDivElement>(null)
    const horizontalScrollRef = useRef<HTMLDivElement>(null)

    definition.filter = definition.filter || {conditions: [], match: Match.All}
    definition.sorts = definition.sorts || []

    const database = databaseStore[definition.databaseId]

    const isPublishedView = !!maybeShared
    const editable = !definition.lockContent && !isPublishedView && !!accessLevel

    const maybeTemplate = useMaybeTemplate()

    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable)
    let canEditData: boolean = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable)

    // Sync horizontal scrolling between content and scrollbar
    useEffect(() => {
        const contentElement = contentScrollRef.current
        const scrollbarElement = horizontalScrollRef.current

        if (!contentElement || !scrollbarElement) return

        const syncContentScroll = () => {
            scrollbarElement.scrollLeft = contentElement.scrollLeft
        }

        const syncScrollbarScroll = () => {
            contentElement.scrollLeft = scrollbarElement.scrollLeft
        }

        contentElement.addEventListener('scroll', syncContentScroll)
        scrollbarElement.addEventListener('scroll', syncScrollbarScroll)

        return () => {
            contentElement.removeEventListener('scroll', syncContentScroll)
            scrollbarElement.removeEventListener('scroll', syncScrollbarScroll)
        }
    }, [])

    const getFieldsToDisplay = (): DatabaseColumn[] => {
        const fieldsToDisplay: DatabaseColumn[] = []

        if (!database) return fieldsToDisplay
        const dbDefinition = database.database.definition
        if (!dbDefinition) return fieldsToDisplay

        let {columnsOrder, columnPropsMap} = definition
        columnsOrder = Array.isArray(columnsOrder) ? columnsOrder : []
        columnPropsMap = columnPropsMap || {}

        for (const key of dbDefinition.columnIds) {
            if (!columnsOrder.includes(key)) columnsOrder.push(key)
            if (!columnPropsMap[key]) columnPropsMap[key] = {}
        }

        for (const id of columnsOrder) {
            const dbCol = dbDefinition.columnsMap[id]
            if (!dbCol) continue
            if (columnPropsMap[id].isHidden) continue
            
            fieldsToDisplay.push(dbCol)
        }

        return fieldsToDisplay
    }

    const getProcessedRows = (): DataViewRow[] => {
        if (!database) return []

        const sortOptions: DbRecordSort[] = []
        if (sorts.length > 0) {
            sortOptions.push(...sorts)
        } else if (definition.sorts.length > 0) {
            sortOptions.push(...definition.sorts)
        }
        if (sortOptions.length === 0) sortOptions.push({columnId: MagicColumn.CreatedAt, order: Sort.Asc})

        const colIds: string[] | null = cache.getCache('newlyCreatedRecords')
        const createdColIds: string[] = colIds && Array.isArray(colIds) ? colIds : []

        const {rows} = filterAndSortRecords(
            database,
            members,
            databaseStore,
            definition.filter,
            filter,
            sortOptions,
            workspace.workspaceMember.userId,
            '',
            maybeRecord?.recordInfo.record.id,
            createdColIds
        )
        return rows
    }

    const filteredRows = getProcessedRows()
    const rows = searchFilteredRecords(search, filteredRows)
    const fieldsToDisplay = getFieldsToDisplay()

    if (!database) {
        return (
            <div className="h-64 flex items-center justify-center">
                <div className="text-center">
                    <p className="text-sm text-gray-500">Loading...</p>
                </div>
            </div>
        )
    }

    const titleColOpts = getDatabaseTitleCol(database.database);

    const isInRecordTab = !!maybeRecord;
    const currentRecordId = maybeRecord?.recordInfo?.record?.id;
    
    const isOnRecordPage = pathname.includes('/records/') && pathname.endsWith(`/records/${currentRecordId}`);
    
    const handleRecordClick = (recordId: string, recordDatabaseId: string) => {
        if (definition.lockContent) return;
        
        openRecord(recordId, recordDatabaseId);
    };

    return (
        <div className="w-full h-full overflow-hidden listView">
            <div className="overflow-hidden size-full flex flex-col">
                {!isPublishedView && definition.lockContent && (
                    <div className='p-2 border-b bg-yellow-50 text-xs text-center border-neutral-300 font-medium'>
                        Content is locked, record navigation is disabled
                    </div>
                )}
                
                <div className="flex-1 overflow-hidden scroll-wrapper">
                    <div className="content-container">
                        <div 
                            ref={contentScrollRef}
                            className="content-horizontal-scroll"
                        >
                            <div className="scroll-container">
                                <div className="border-b rowGrid border-neutral-200 header-row">
                                    <div className="text-xs text-black font-bold bg-white check !w-1">
                                    </div>
                                    <div className="text-xs text-black font-bold bg-white fluid">Title</div>
                                    {fieldsToDisplay.map((field) => (
                                        <div key={field.id} className="text-xs text-black font-bold bg-white">
                                            {field.title}
                                        </div>
                                    ))}
                                </div>

                                {rows.length === 0 ? (
                                    <div className="text-center py-8 text-gray-500">
                                        No records found
                                    </div>
                                ) : (
                                    rows.map((row) => {
                                        const title = getRecordTitle(
                                            row.record,
                                            titleColOpts.titleColId,
                                            titleColOpts.defaultTitle,
                                            titleColOpts.isContacts,
                                            database.database,
                                            members
                                        );

                                        return (
                                            <div 
                                                key={row.id} 
                                                className={`rowGrid border-b ${definition.lockContent ? 'cursor-default' : 'hover:bg-neutral-100 cursor-pointer'}`}
                                                onClick={(e) => {
                                                    const target = e.target as HTMLElement;
                                                    const isInteractiveField = target.closest('.r-button-group, .r-scannable-code, .r-files, button, [role="button"]');
                                                    
                                                    console.log('Row click:', { target: target.tagName, isInteractiveField, className: target.className });
                                                    
                                                    if (!isInteractiveField) {
                                                        handleRecordClick(row.record.id, row.record.databaseId);
                                                    }
                                                }}
                                            >
                                                <div className="text-xs check !w-1">
                                                </div>
                                                <div className="text-xs flex flex-col fluid">
                                                    <div className="title-text text-xs font-semibold">
                                                        {title || "Untitled"}
                                                    </div>
                                                    <div className="flex gap-2 text-xs text-muted-foreground pt-1">
                                                        <span className="truncate">{database.database.name}</span>
                                                        <span className="flex-shrink-0">&bull;</span>
                                                        <span className="truncate">{timeAgo(new Date(row.updatedAt))}</span>
                                                    </div>
                                                </div>

                                                {fieldsToDisplay.map((field) => (
                                                    <div key={field.id} className="text-xs truncate">
                                                        <ListFieldRenderer
                                                            field={field}
                                                            row={row}
                                                            databaseId={definition.databaseId}
                                                            isPublishedView={isPublishedView}
                                                            lockContent={definition.lockContent || false}
                                                        />
                                                    </div>
                                                ))}
                                            </div>
                                        );
                                    })
                                )}
                            </div>
                        </div>
                    </div>

                    <div 
                        ref={horizontalScrollRef}
                        className="horizontal-scroll-container"
                    >
                        <div className="horizontal-scroll-content">
                            <div className="rowGrid" style={{ visibility: 'hidden', height: '1px' }}>
                                <div className="check !w-1"></div>
                                <div className="fluid"></div>
                                {fieldsToDisplay.map((field) => (
                                    <div key={field.id}></div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}