"use client";

import { useViews } from "@/providers/views";
import { usePeekStack } from "@/providers/peekStack";
import { useMaybeRecord } from "@/providers/record";


export const useStackedPeek = () => {
    const { setPeekRecordId } = useViews();
    const { isInPeekContext, pushPeek } = usePeekStack();
    const maybeRecord = useMaybeRecord();

    const openRecord = (recordId: string, databaseId: string) => {
        // Always try to push a new peek if a record ID is provided
        if (recordId) {
            const inRecordContext = !!maybeRecord || isInPeekContext;

            if (inRecordContext) {
                pushPeek(recordId, databaseId);
            } else {
                setPeekRecordId(recordId);
            }
        } 
    };

    return {
        openRecord,
        isInPeekContext: !!maybeRecord || isInPeekContext
    };
};
