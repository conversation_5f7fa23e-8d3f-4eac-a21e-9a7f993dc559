"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { TriangleExclamationIcon } from "@/components/icons/FontAwesomeRegular";
import { RequiredAsterisk } from "@/components/custom-ui/requiredAsterisk";
import { useViews } from "@/providers/views";
import { useAlert } from "@/providers/alert";
import { useWorkspace } from "@/providers/workspace";
import useForceRender from "@/components/custom-ui/forceRender";
import { useAuth } from "@/providers/user";
import { useRouter } from "next/navigation";
import { useBroadcast } from "@/providers/broadcast";
import { ButtonGroupColumn, ActionButton, DbCondition, DbRecordFilter } from "opendb-app-db-utils/lib/typings/db";
import { FormFieldAreaProps } from "./text";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { getButtonIcon } from "@/components/workspace/main/views/table/renderer/fields/buttonGroup";
import { useStackedPeek } from "@/providers/stackedPeek";
import { useMaybeRecord } from "@/providers/record";
import { 
  executeDeclarativeAction, 
  useInputDialog, 
  createActionServices,
  evaluateButtonState 
} from "@/utils/buttonActionHelpers";
import { ButtonState, ActionContext, ActionServices, RecordData, DatabaseData, WorkspaceData, TokenData, User } from "@/utils/buttonAction";

export const ButtonGroupFieldArea = (props: FormFieldAreaProps) => {
  const { updateRecordValues, deleteRecords, directDeleteRecords } = useViews();
  const { confirm, toast } = useAlert();
  const { databaseStore, workspace } = useWorkspace();
  const { forceRender } = useForceRender();
  const { token, user } = useAuth();
  const router = useRouter();
  const { sendMessage } = useBroadcast();
  const maybeRecord = useMaybeRecord();
  
  const inputDialog = useInputDialog();

    const { id, columnsMap, databaseId, values } = props;
  const { openRecord } = useStackedPeek(databaseId);
    const column = columnsMap[id] as ButtonGroupColumn;
    const buttons = column.buttons || [];

  const database = databaseStore?.[databaseId];
  if (!database) {
    return null;
  }

  const row = {
    id: String(values.id || ""),
    recordValues: values,
  };

  const handleButtonClick = async (button: ActionButton) => {
    if (!button?.actions?.length) {
      toast.info("This button has no actions configured");
      return;
    }

    const context: ActionContext = {
      record: row as unknown as RecordData,
      database: database as unknown as DatabaseData,
      workspace: workspace as unknown as { workspace?: WorkspaceData; id?: string; domain?: string },
      token: token as TokenData,
      user: user as User,
      databaseId,
      // Manually add the parent record context if we are in a peek view
      parentRecord: maybeRecord ? {
        id: maybeRecord.recordInfo.record.id,
        databaseId: maybeRecord.database.id,
      } : undefined
    };

    const services = createActionServices({
      updateRecordValues: updateRecordValues as (databaseId: string, recordIds: string[], values: Record<string, unknown>) => Promise<boolean>,
      deleteRecords,
      directDeleteRecords,
      setPeekRecord: (recordId: string, databaseId: string) => openRecord(recordId, databaseId, context.parentRecord),
      confirm,
      toast,
      router,
      forceRender,
      sendMessage: (message: string) => sendMessage('info', 'action', { message })
    }, inputDialog);

    const urlsToOpen: string[] = [];
    let actionSucceeded = true;

    for (const action of button.actions) {
      const { success, result } = await executeDeclarativeAction(action as { actionType: string; props?: Record<string, unknown> }, context, services, databaseStore);
      
      if (!success) {
        actionSucceeded = false;
        break; 
      }

      if (action.actionType === 'openUrl' && result?.url) {
        urlsToOpen.push(result.url);
      }
    }

    if (urlsToOpen.length > 0) {
      services.toast.success(`Opening ${urlsToOpen.length} URL(s)...`);
      urlsToOpen.forEach(url => {
        window.open(url, '_blank', 'noopener,noreferrer');
      });
    }

    if (actionSucceeded) {
    } else {
    }

    forceRender();
  };

  const buttonStates = buttons.map(button => ({
    button,
    state: evaluateButtonState(button as { actions?: Array<{actionType: string; props?: Record<string, unknown>}>; visibleIf?: DbCondition[]; enabledIf?: DbCondition[]; visibleIfFilter?: DbRecordFilter; enabledIfFilter?: DbRecordFilter }, values || {})
  }));

  const visibleButtons = buttons.filter(button => {
    const buttonState = buttonStates.find(bs => bs.button.id === button.id)?.state;
    return buttonState?.visible !== false; 
  });

  if (!visibleButtons.length) {
    return (
      <div className="flex flex-wrap gap-2 button-group-container">   
      </div>
    );
  }

  return (
    <>
      <div className="flex flex-wrap gap-2 button-group-container">
        {visibleButtons.length === 1 ? (
          (() => {
            const button = visibleButtons[0];
            const buttonState = buttonStates.find(bs => bs.button.id === button.id)?.state;
            const hasError = buttonState?.state === ButtonState.ERROR;
            const isDisabled = buttonState?.state === ButtonState.DISABLED;
            
            return (
              <Button
                onClick={() => !isDisabled && !hasError && handleButtonClick(button)}
                disabled={isDisabled || hasError}
                variant="outline"
                className={`text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center ${
                  hasError ? 'bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-600 cursor-not-allowed' :
                  isDisabled ? 'bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed' :
                  ''
                }`}
              >
                {hasError ? (
                  <TriangleExclamationIcon className="size-3 text-red-500" />
                ) : isDisabled ? (
                  <TriangleExclamationIcon className="size-3 text-red-500" />
                ) : (
                  getButtonIcon(button)
                )}
                                  <span className="truncate">{button.label || 'Action'}</span>
              </Button>
            );
          })()
        ) : (
          <div className="flex items-center gap-1 flex-wrap sm:flex-nowrap w-full">
            {(() => {
              const button = visibleButtons[0];
              const buttonState = buttonStates.find(bs => bs.button.id === button.id)?.state;
              const hasError = buttonState?.state === ButtonState.ERROR;
              const isDisabled = buttonState?.state === ButtonState.DISABLED;
              
              return (
                <Button
                  onClick={() => !isDisabled && !hasError && handleButtonClick(button)}
                  disabled={isDisabled || hasError}
                  variant="outline"
                  className={`text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center ${
                    hasError ? 'bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-600 cursor-not-allowed' :
                    isDisabled ? 'bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed' :
                    ''
                  }`}
                >
                  {hasError ? (
                    <TriangleExclamationIcon className="size-3 text-red-500" />
                  ) : isDisabled ? (
                    <TriangleExclamationIcon className="size-3 text-red-500" />
                  ) : (
                    getButtonIcon(button)
                  )}
                  <span className="truncate">{button.label || 'Action'}</span>
                </Button>
              );
            })()}

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button className="text-xs rounded-full p-1.5 h-auto" variant="outline">
                  <ChevronDownIcon className="size-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48 z-[99999]" sideOffset={5}>
                {visibleButtons.slice(1).map((button) => {
                  const buttonState = buttonStates.find(bs => bs.button.id === button.id)?.state;
                  const hasError = buttonState?.state === ButtonState.ERROR;
                  const isDisabled = buttonState?.state === ButtonState.DISABLED;
                  
                  return (
                    <DropdownMenuItem
                      key={button.id}
                      onClick={() => !isDisabled && !hasError && handleButtonClick(button)}
                      disabled={isDisabled || hasError}
                      className={`text-xs p-2 gap-2 flex items-center ${
                        hasError ? 'text-gray-600 cursor-not-allowed' :
                        isDisabled ? 'text-gray-400 cursor-not-allowed' :
                        ''
                      }`}
                    >
                      {hasError ? (
                        <TriangleExclamationIcon className="size-3 text-red-500" />
                      ) : isDisabled ? (
                        <TriangleExclamationIcon className="size-3 text-red-500" />
                      ) : (
                        getButtonIcon(button)
                      )}
                      <span className="truncate">{button.label || 'Action'}</span>
                    </DropdownMenuItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      <Dialog open={inputDialog.inputDialogOpen} onOpenChange={inputDialog.setInputDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{inputDialog.inputDialogTitle}</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-gray-500 mb-4">{inputDialog.inputDialogMessage}</p>
            <Input
              value={inputDialog.inputValue}
              onChange={(e) => inputDialog.setInputValue(e.target.value)}
              placeholder="Enter your input here"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" size="sm" className="text-xs" onClick={() => inputDialog.setInputDialogOpen(false)}>Cancel</Button>
            <Button variant="outline" size="sm" className="text-xs" onClick={inputDialog.handleInputSubmit}>Submit</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}; 
