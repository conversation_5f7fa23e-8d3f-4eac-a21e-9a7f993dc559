import React, { useMemo } from 'react';
import { format, isToday, setHours } from 'date-fns';
import { cn } from '@/lib/utils';
import { CalendarEvent } from '@/typings/page';
import { CalendarEventSegment } from './CalendarEventSegment';
import { NoEvents } from './NoEvents';
import {
  eventsToSegments,
  getSegmentsForDay,
  getSegmentHeight,
  getSegmentTopOffset,
  getAllDaySegments,
  getTimeSlotSegments,
} from '@/utils/multiDayEventUtils';
import { calculateLayout } from '@/utils/eventCollisionUtils';
import { AllDayRow } from './AllDayRow';
import { useDroppable } from '@dnd-kit/core';
import { MoreIndicator } from './MoreIndicator';

interface DayViewProps {
  selectedDate: Date;
  events: CalendarEvent[];
  selectedEvent: string | null;
  setSelectedEvent: (id: string) => void;
  openAddEventForm: (date: Date) => void;
  canEditData: boolean;
  savedScrollTop: React.MutableRefObject<number>;
  handleEventClick: (event: CalendarEvent) => void;
  activeDragData: any;
}

const TimeSlot = ({ 
  hour, 
  date, 
  onDoubleClick, 
  children 
}: {
  hour: number;
  date: Date;
  onDoubleClick: () => void;
  children: React.ReactNode;
}) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `timeslot-day-${format(date, 'yyyy-MM-dd')}-${hour}`,
    data: {
      date: date,
      hour: hour,
      type: 'timeslot-day'
    }
  });
  return (
    <div
      ref={setNodeRef}
      className={cn(
        "flex-1 relative min-h-[60px] cursor-pointer",
        isOver && "bg-blue-50"
      )}
      style={{ height: '60px' }}
      onDoubleClick={onDoubleClick}
    >
      {children}
    </div>
  );
};

export const DayView: React.FC<DayViewProps> = ({
  selectedDate,
  events,
  selectedEvent,
  setSelectedEvent,
  openAddEventForm,
  canEditData,
  savedScrollTop,
  handleEventClick,
  activeDragData,
}) => {
  const hours = Array.from({ length: 24 }, (_, i) => i);
  
  // Memoize event segments to prevent unnecessary recalculations
  const daySegments = useMemo(() => {
    const allSegments = eventsToSegments(events);
    return getSegmentsForDay(allSegments, selectedDate);
  }, [events, selectedDate]);

  // Separate all-day and time-slot segments
  const allDaySegments = useMemo(() => getAllDaySegments(daySegments), [daySegments]);
  const timeSlotSegments = useMemo(() => getTimeSlotSegments(daySegments), [daySegments]);

  // Calculate layout for overlapping segments
  const { segmentLayouts } = useMemo(() => {
    return calculateLayout(timeSlotSegments);
  }, [timeSlotSegments]);

  // Memoize current time position
  const currentTimePosition = useMemo(() => 
    isToday(selectedDate) 
      ? {
          hour: new Date().getHours(),
          minutes: new Date().getMinutes()
        } 
      : null, 
    [selectedDate]
  );

  // Render empty state when no events
  const renderEmptyState = () => (
    <NoEvents
      title="No events scheduled"
      message={isToday(selectedDate)
        ? "You have a free day ahead! Add an event to get started."
        : `${format(selectedDate, 'EEEE, MMMM d')} is completely free.`}
      showCreateButton={canEditData}
      onCreate={() => {
        const newDate = new Date(selectedDate);
        newDate.setHours(9, 0, 0, 0);
        openAddEventForm(newDate);
      }}
    />
  );

  // Render time slots with events
  const renderTimeSlots = () => (
    <div className="flex-1 overflow-auto relative bg-white" id="day-view-container">
      {hours.map((hour, i) => (
        <div 
          key={i} 
          className={cn(
            "flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors",
            i === hours.length - 1 && "border-b-neutral-300"
          )} 
          style={{ height: '60px' }}
        >
          {/* Time Label */}
          <div className="flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-300 sticky left-0 bg-white z-10 w-14 lg:w-20">
            <div className="text-right">
              <div className="text-xs font-semibold">
                {format(setHours(selectedDate, hour), 'h')}
              </div>
              <div className="text-xs text-black opacity-60">
                {format(setHours(selectedDate, hour), 'a')}
              </div>
            </div>
          </div>

          {/* Time Slot */}
          <TimeSlot
            hour={hour}
            date={selectedDate}
            onDoubleClick={() => {
              if (canEditData) {
                const newDate = new Date(selectedDate);
                newDate.setHours(hour, 0, 0, 0);
                openAddEventForm(newDate);
              }
            }}
          >
            {segmentLayouts.map((layout) => {
              const segmentStart = layout.segment.startTime;
              const isFirstHour = segmentStart.getHours() === hour;

              if (!isFirstHour) return null;

              const segmentHeight = getSegmentHeight(layout.segment);
              const topOffset = getSegmentTopOffset(layout.segment);

              return (
                <CalendarEventSegment
                  key={layout.segment.id}
                  segment={layout.segment}
                  style={{
                    height: `${segmentHeight}px`,
                    position: 'absolute',
                    top: `${topOffset}px`,
                    left: `${layout.left}%`,
                    width: `${layout.width}%`,
                    zIndex: selectedEvent === layout.segment.originalEventId ? 20 : layout.zIndex,
                    paddingRight: '2px', // Add small gap between columns
                    border: layout.hasOverlap ? '1px solid white' : 'none', // White border for overlapping events
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    const container = document.getElementById('day-view-container');
                    if(container) {
                      savedScrollTop.current = container.scrollTop;
                    }
                    setSelectedEvent(layout.segment.originalEventId);
                    handleEventClick(layout.segment.originalEvent);
                  }}
                  view="day"
                  isDragging={activeDragData?.payload?.id === layout.segment.id}
                />
              );
            })}
          </TimeSlot>
        </div>
      ))}

      {/* Current Time Indicator */}
      {currentTimePosition && (
        <div
          className="absolute flex items-center z-30 pointer-events-none left-14 lg:left-20"
          style={{
            top: `${(currentTimePosition.hour + currentTimePosition.minutes / 60) * 60}px`,
            right: '4px'
          }}
        >
          <div className="w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5" />
          <div className="flex-1 border-t-2 border-red-500 shadow-sm" />
        </div>
      )}
    </div>
  );

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="text-center bg-white border-b border-neutral-300 py-2 px-2 lg:px-0">
        <div className="font-semibold text-black mb-1 text-xs">
          {format(selectedDate, 'EEEE')}
        </div>
        <div className={cn(
          "inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full",
          isToday(selectedDate)
            ? "bg-black text-white"
            : "text-black hover:bg-neutral-100"
        )}>
          {format(selectedDate, 'd')}
        </div>
      </div>

      {/* All-Day Section */}
      {daySegments.length > 0 && (
        <AllDayRow
          selectedDate={selectedDate}
          segments={allDaySegments}
          selectedEvent={selectedEvent}
          setSelectedEvent={setSelectedEvent}
          handleEventClick={handleEventClick}
          canEditData={canEditData}
          openAddEventForm={openAddEventForm}
          view="day"
          activeDragData={activeDragData}
        />
      )}

      {/* Main Content */}
      {daySegments.length === 0 
        ? renderEmptyState() 
        : renderTimeSlots()}
    </div>
  );
};