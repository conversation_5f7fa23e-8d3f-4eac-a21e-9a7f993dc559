"use client";

import {<PERSON><PERSON>} from "@/components/ui/button";
import React, {createContext, PropsWithChildren, useContext, useEffect, useMemo, useRef, useState} from "react";
import {EnvelopeIcon, MagnifyingGlassCircleIcon, SparklesIcon} from "@heroicons/react/24/outline";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {ArrowUpFromArcIcon, ArrowUpRightAndArrowDownLeftFromCenterIcon, ArrowUpWideShortIcon, BarcodeReadIcon, CirclePlusIcon, DatabaseIcon, FilterListIcon, SquarePenIcon, TrashListIcon} from "@/components/icons/FontAwesomeRegular";
import {usePage} from "@/providers/page";
import {AccessLevel, View} from "@/typings/page";
import {PageLoader} from "@/components/custom-ui/loader";
import {useWorkspace} from "@/providers/workspace";
import {useRouter, useSearchParams} from "next/navigation";
import {TableViewDefinition, ViewType} from "opendb-app-db-utils/lib/typings/view";
import {getViewPublicUrl} from "@/api/page";
import {useAlert} from "@/providers/alert";
import {copyToClipboard} from "@/utils/clipboard";
import {useViews, useViewFiltering, useViewSelection } from "@/providers/views";

import {ViewDefinition} from "opendb-app-db-utils/lib/typings/view";
import {ColumnsReorder} from "@/components/workspace/main/views/common/columnsReorder";
import {ViewCreator} from "@/components/workspace/main/views/common/viewCreator";
import {ViewSwitcher} from "@/components/workspace/main/views/common/viewSwitcher";
import {ViewFilter} from "@/components/workspace/main/views/common/viewFilter";
import {Database} from "@/typings/database";
import {ViewSort} from "@/components/workspace/main/views/common/viewSort";
import {DashboardViewMoreOptions, DocViewMoreOptions, FormViewMoreOptions, ViewMoreOptions} from "@/components/workspace/main/views/common/viewMoreOptions";
import {SummaryColumnGroupBy, SummaryColumnsReorder} from "@/components/workspace/main/views/summaryTable/renderers/header";
import {DropdownMenu, DropdownMenuContent, DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import {cn} from "@/lib/utils";
import {ObjectType} from "opendb-app-db-utils/lib/typings/common";
import {TagItem} from "@/components/workspace/main/views/table/renderer/common/tag";
import {CustomSelect} from "@/components/custom-ui/customSelect";
import {SendEmailWrapperForView} from "@/components/workspace/main/emails/sendEmailWrapper";
import {UpdateRecords} from "@/components/workspace/main/common/updateRecords";
import {EventType, pushEvent} from "@/api/account";
import {useAuth} from "@/providers/user";
import {ShareView} from "@/components/workspace/main/views/common/shareView";
import {Sheet, SheetClose, SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetTitle, SheetTrigger,} from "@/components/ui/sheet"
import {DatabaseFieldDataType, DbRecordFilter, LinkedColumn, Match, ProcessedDbRecord} from "opendb-app-db-utils/lib/typings/db";
// import {ScrollArea} from "@/components/ui/scroll-area";
import {RecordProvider} from "@/providers/record";
import {membersToPersons} from "@/components/workspace/main/views/table/renderer/fields/person";
import {LinkedDatabases, transformRawRecords} from "opendb-app-db-utils/lib/utils/db";
import {useMaybeTemplate} from "@/providers/template";
import Link from "next/link";
import {useMaybeShared} from "@/providers/shared";
import {RecordExtras} from "@/components/workspace/main/record/components/recordExtras";
import {IDetectedBarcode, Scanner} from '@yudiel/react-qr-scanner';
import {Popover, PopoverContent, PopoverTrigger,} from "@/components/ui/popover"
import {filterAndSortRecords, searchFilteredRecords} from "@/components/workspace/main/views/table";
import {useMaybeRecord} from "@/providers/record";
import {RecordTabViewsProvider} from "@/providers/recordTabViews";
import {AddRecordModal} from "@/components/workspace/main/views/AddRecordModal";
import {useStackedPeek} from "@/providers/stackedPeek";


export interface ViewGroupProps {
    context?: "database" | "page"|'record_tab'
    parentId: string
    viewId: string
}

export const ViewContext = createContext<{ context?: "database" | "page" | "record_tab" }>({context: 'page'});
export const useViewContext = () => useContext(ViewContext);

export const ViewsRootLayout = (props: PropsWithChildren<ViewGroupProps>) => {
    const {url} = useWorkspace()
    const {token} = useAuth()

    const searchParams = useSearchParams()
    const documentId = searchParams.get('documentId')

    const router = useRouter()
    const {viewsMap, accessLevel, page} = usePage()
    const [newView, setNewView] = useState(false)
          
    const {createRecords, deleteRecords, smartUpdateViewDefinition, peekRecordId, setPeekRecordId} = useViews()
    const {filter, sorts, search, setFilter, setSorts, setSearch} = useViewFiltering()
    const {selectedIds, setSelectedIds} = useViewSelection()
    const {databaseStore, workspace} = useWorkspace()
    const {toast} = useAlert()
    const maybeTemplate = useMaybeTemplate()
    const maybeRecord = useMaybeRecord()
    const {openRecord} = useStackedPeek()
    
    // State for AddRecordModal
    const [showAddModal, setShowAddModal] = useState(false)

    // For record_tab context, get view from database metadata
    let view: View | null = null
    if (props.context === 'record_tab') {
        const database = databaseStore[props.parentId]?.database
        if (database && database.meta && database.meta.recordViewsMap) {
            view = database.meta.recordViewsMap[props.viewId] || null
        }
    }

    if (!view) {
        view = viewsMap[props.viewId] || null
    }
    
    let viewType: ViewType | undefined = view?.type

    // Context-aware update function that automatically detects record tab vs regular views
    const contextAwareUpdateViewDefinition = (update: Partial<ViewDefinition>) => {
        const isRecordTab = props.context === 'record_tab'
        const databaseId = isRecordTab ? props.parentId : undefined
        
        return smartUpdateViewDefinition(
            view?.id || '', 
            view?.pageId || '', 
            update, 
            { databaseId, isRecordTab }
        )
    }

    const canContact = false;

    const canEdit = accessLevel && [AccessLevel.Full, AccessLevel.Edit].includes(accessLevel)
    const hasFullAccess = accessLevel && accessLevel === AccessLevel.Full

    let viewFilter: DbRecordFilter | null = {conditions: [], match: Match.All}
    if (viewType === ViewType.Board || viewType === ViewType.Table) {
        const definition = view.definition as TableViewDefinition
        viewFilter = definition.filter || viewFilter
    }

    const handleAddRecord = () => {
        if (!view) return
        if (view.type !== ViewType.Board && view.type !== ViewType.Table) return
        
        // Different behavior based on context
        if (props.context === 'record_tab') {
            // In record tabs: Use smart modal (especially for cross-database)
            setShowAddModal(true)
        } else {
            // In main views: Direct record creation (original behavior)
            handleDirectAddRecord()
        }
    }

    const handleDirectAddRecord = async () => {
        if (!view) return
        const definition = view.definition as TableViewDefinition
        if (!definition.databaseId) return
        
        // Create record directly without modal (original behavior)
        const rS = await createRecords(definition.databaseId, [{}])
        
        // Only peek if there are filters/search (original logic)
        const shouldPeek = (search && search.trim()) || filter?.conditions?.length > 0 || (viewFilter && viewFilter.conditions && viewFilter?.conditions?.length > 0)
        if (rS && rS.records && rS.records.length > 0 && shouldPeek) {
            setPeekRecordId(rS.records[0].id)
        }
    }

    const handleRecordCreated = (recordId: string) => {
        if (!view) return
        const definition = view.definition as TableViewDefinition
        if (!definition.databaseId) return
        
        // In record tabs: Stack the newly created record
        openRecord(recordId, definition.databaseId)
        setShowAddModal(false)
    }

    const deleteSelected = async () => {
        if (!view) return
        if (view.type !== ViewType.Board && view.type !== ViewType.Table) return
        const definition = view.definition as TableViewDefinition

        if (!definition.databaseId) return
        const {databaseId} = definition

        await deleteRecords(databaseId, selectedIds)
    }
    const copySharedUrl = () => {
        if (!view) return
        const {id, name} = view
        const url = getViewPublicUrl(id, name)
        copyToClipboard(url)
        toast.success("Link copied to clipboard")
    }

    let database: Database | null = null
    let dbId = ''
    if (view && [ViewType.Table, ViewType.Board, ViewType.SummaryTable, ViewType.Form, ViewType.Calendar, ViewType.ListView].includes(viewType)) {
        dbId = (view.definition as TableViewDefinition).databaseId
        database = databaseStore[dbId] ? databaseStore[dbId].database : null
    }
  
    const currentViewIdRef = useRef(props.viewId || '')

    const viewId = props.viewId
    const pageId = view?.pageId || ''
    const workspaceId = workspace.workspace.id
    const viewExists = !!view

    useEffect(() => {
        if (!viewExists) return
        const timeout = setTimeout(async () => {
            const databaseId = dbId ? dbId : undefined
            await pushEvent(token?.token || '', {workspaceId, pageId, event: EventType.View, databaseId, viewId})
        }, 3000)
        return () => {
            if (timeout) clearTimeout(timeout)
        }
    }, [dbId, token?.token, workspaceId, pageId, viewId, viewExists])

    useEffect(() => {
        if (props.viewId === currentViewIdRef.current) return
        currentViewIdRef.current = props.viewId
        setSorts([])
        setFilter({conditions: [], match: Match.All})
        setSearch('')
        setSelectedIds([])
        setPeekRecordId('')
    }, [props.viewId]) 
    // , [props.viewId, setFilter, setSorts, setSearch, setSelectedIds, setPeekRecordId])
    // const [peekRecordId, setPeekRecordId] = useState("")

    return (
        <ViewContext.Provider value={{context: props.context}}>
            {!view && <>
                <PageLoader size="full" error="The requested content does not exists" cta={{
                    label: "Go Home",
                    onClick: () => router.replace(url())
                }}/>
            </>}


            {view && <div className="w-full h-full overflow-hidden flex flex-col">
                {/*View Group Header*/}
              {/*<div className="p-2 px-4 h-12 flex items-center border-b border-neutral-300">*/}
                {/* Hide header for calendar view when it has no controls */}
                {viewType !== ViewType.Calendar && <div className="p-2 h-12 flex items-center border-b border-neutral-300 gap-0.5">
                    {props.context !== 'record_tab' && <>
                        <ViewSwitcher context={props.context} viewId={props.viewId}
                                      creatable={canEdit}
                                      editable={canEdit}
                                      deletable={hasFullAccess}
                                      cloneable={hasFullAccess}
                                      requestNewView={() => setNewView(true)}/>
                        {canEdit && <ViewCreator context={props.context} open={newView} setOpen={setNewView}/>}
                        {!maybeTemplate && <ShareView
                            view={view}
                            page={page}
                            documentId={documentId || ''}
                            domain={workspace.workspace.domain}
                            triggerAlign={"start"}
                            trigger={<Button variant="ghost"
                                             className={cn("text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium", view.isPublished && 'text-blue-600 font-semibold')}>
                                <ArrowUpFromArcIcon className='size-3'/> Share View
                            </Button>}/>}

                        {props.context === 'page' && database && <>
                            <PageContextSourceDatabase database={database}/>
                        </>}

                        <div className="flex-1"></div>
                    </>}

                    {props.context === 'record_tab' && <div className="flex-1"></div>}

                    {database && [ViewType.Table, ViewType.Board].includes(viewType as ViewType) && <>
                        {!(view.definition as TableViewDefinition).lockContent && <>
                                                    {/*{(search && search.trim()) || filter?.conditions?.length > 0 || (viewFilter && viewFilter.conditions && viewFilter?.conditions?.length > 0) ? <>*/}
                            {/*    */}
                            {/*</> : <>*/}
                            {/*    <Button variant="ghost"*/}
                            {/*            onClick={addRecord}*/}
                            {/*            className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium">*/}
                            {/*        <CirclePlusIcon className="size-3"/>*/}
                            {/*        Add*/}
                            {/*    </Button>*/}
                            {/* </>}*/}
                            {/*{(search && search.trim()) || filter?.conditions?.length > 0 || (viewFilter && viewFilter.conditions && viewFilter?.conditions?.length > 0) ? <>*/}
                            {/*    <PeekRecord trigger={<Button variant="ghost"*/}
                            {/*                                 className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium">*/}
                            {/*        <CirclePlusIcon className="size-3"/>*/}
                            {/*        Add*/}
                            {/*    </Button>}>*/}
                            {/*    </PeekRecord>*/}
                            {/*</> : <>*/}
                            {/*     <Button variant="ghost"*/}
                            {/*             onClick={addRecord}*/}
                            {/*             className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium">*/}
                            {/*         <CirclePlusIcon className="size-3"/>*/}
                            {/*         Add*/}
                            {/*     </Button>*/}
                            {/* </>}*/}
                            {/*{!(search || search.trim()) && selectedIds.length === 0 && viewType === ViewType.Table && <>*/}
                            {/*    <Button variant="ghost"*/}
                            {/*            onClick={addRecord}*/}
                            {/*            className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium">*/}
                            {/*        <CirclePlusIcon className="size-3"/>*/}
                            {/*        Add*/}
                            {/*    </Button>*/}
                            {/*</>}*/}

                            {canEdit && viewType === ViewType.Table && <>
                                <Button variant="ghost"
                                        onClick={handleAddRecord}
                                        className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium">
                                    <CirclePlusIcon className="size-3"/>
                                    Add
                                </Button>
                            </>}
                            {selectedIds.length > 0 && <>
                                <span className='font-semibold text-xs text-blue-600 select-none'>{selectedIds.length} &nbsp;selected</span>
                                <UpdateRecords
                                    trigger={<Button variant="ghost"
                                                     className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium">
                                        <SquarePenIcon className="size-3"/>
                                        Update
                                    </Button>}
                                    database={database}
                                    ids={selectedIds}
                                    onUpdate={() => setSelectedIds([])}
                                />
                                <Button variant="ghost"
                                        onClick={deleteSelected}
                                        className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium">
                                    <TrashListIcon className="size-3"/>
                                    Delete
                                </Button>

                            </>}

                        </>}

                    </>}
                    {database && [ViewType.Table, ViewType.Board, ViewType.SummaryTable, ViewType.ListView].includes(viewType as ViewType) ? <>
                        {canContact && props.context !== 'record_tab' && <>
                            <Button variant="ghost"
                                    className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium">
                                <SparklesIcon className="size-4"/>
                                Enrich
                            </Button>
                            <Button variant="ghost"
                                    className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium">
                                <EnvelopeIcon className="size-4"/>
                                Message
                            </Button>
                        </>}
                        {canEdit && props.context !== 'record_tab' && <SendEmailWrapperForView
                            database={database}
                            view={view}/>}

                        <ViewFilter
                            database={database}
                            trigger={
                                <Button variant="ghost"
                                        className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium">
                                    <FilterListIcon className="size-3"/>
                                    {filter.conditions.length > 0 ?
                                     `${filter.conditions.length} filters` :
                                     'Filter'}
                                </Button>
                            }
                            filter={filter}
                            onChange={setFilter}
                            currentRecordId={maybeRecord?.recordInfo.record.id}
                            currentRecordDatabaseId={maybeRecord?.recordInfo.record.databaseId}
                        />

                        {(viewType === ViewType.Table || viewType === ViewType.SummaryTable) && <>
                            <ViewSort
                                database={database}
                                sorts={sorts}
                                onChange={setSorts}
                                trigger={
                                    <Button variant="ghost"
                                            className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium">
                                        <ArrowUpWideShortIcon className="size-3"/>
                                        {sorts.length > 0 ?
                                         `${sorts.length} sorts` :
                                         'Sort'}
                                    </Button>
                                }
                            />
                        </>}

                        <div className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start flex hover:bg-accent focus:bg-accent active:bg-accent items-center whitespace-nowrap font-medium">
                            <Label form="search-input">
                                {/*<MagnifyingGlassIcon className="size-4"/>*/}
                                <MagnifyingGlassCircleIcon className="size-4"/>
                            </Label>
                            <Input placeholder="Search"
                                   value={search}
                                   onChange={e => setSearch(e.target.value)}
                                   className="text-xs transition-all outline-none h-auto !p-0 !ring-0 w-12 focus:w-48 !bg-transparent border-0 shadow-none drop-shadow-none"/>
                        </div>

                        <ScannableCodeScanner
                            onRecordScan={setPeekRecordId}
                            database={database}
                            viewFilter={viewFilter}
                            filter={filter}/>

                        {canEdit && viewType !== ViewType.SummaryTable && <>
                            <ColumnsReorder
                                onDefinitionUpdate={u => contextAwareUpdateViewDefinition(u)}
                                view={view}/>
                        </>}
                        {canEdit && viewType === ViewType.SummaryTable && <>
                            <SummaryColumnGroupBy
                                onDefinitionUpdate={u => contextAwareUpdateViewDefinition(u)}
                                view={view}/>

                            <SummaryColumnsReorder
                                onDefinitionUpdate={u => contextAwareUpdateViewDefinition(u)}
                                view={view}/>
                        </>}
                        {canEdit && <ViewMoreOptions
                            // definition={view.definition as TableViewDefinition}
                            disabled={!canEdit}
                            view={view}
                            database={database}
                            selectedIds={selectedIds}
                            filter={filter}
                            sorts={sorts}
                            search={search}
                            onDefinitionUpdate={u => contextAwareUpdateViewDefinition(u)}
                            currentRecordId={maybeRecord?.recordInfo?.record?.id}
                            currentRecordDatabaseId={maybeRecord?.recordInfo?.record?.databaseId}
                        />}

                        {/*<Button variant="ghost" className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start">*/}
                        {/*    <ArrowUpOnSquareIcon className="size-4"/>*/}
                        {/*    Export*/}
                        {/*</Button>*/}
                        {/*<Button variant="ghost" className="text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start">*/}
                        {/*    <PrinterIcon className="size-4"/>*/}
                        {/*    Print*/}
                        {/*</Button>*/}
                    </> : <>

                     </>}

                    {canEdit && database && viewType === ViewType.Form && props.context !== 'record_tab' && <>
                        <FormViewMoreOptions
                            disabled={!canEdit}
                            view={view}
                            database={database}
                            onDefinitionUpdate={u => contextAwareUpdateViewDefinition(u)}
                        />
                    </>}

                    {canEdit && viewType === ViewType.Dashboard && <>
                        
                        <DashboardViewMoreOptions
                            disabled={!canEdit}
                            view={view}
                            onDefinitionUpdate={u => contextAwareUpdateViewDefinition(u)}
                        />
                    </>}
                    {canEdit && viewType === ViewType.Document && props.context !== 'record_tab' && <>
                        <DocViewMoreOptions
                            disabled={!canEdit}
                            view={view}
                            onDefinitionUpdate={u => contextAwareUpdateViewDefinition(u)}
                        />

                    </>}


                </div>}
                <div className="flex-1 overflow-hidden pr=1">
                    {props.children}
                </div>
            </div>}

            {database && peekRecordId && !maybeRecord && <PeekRecord
                canEdit={canEdit}
                onClose={() => setPeekRecordId('')}
                recordId={peekRecordId} databaseId={database.id}/>}

            {/* AddRecordModal for intelligent record creation */}
            {database && showAddModal && (
                <AddRecordModal
                    open={showAddModal}
                    onClose={() => setShowAddModal(false)}
                    databaseId={database.id}
                    viewFilter={viewFilter || undefined}
                    contextualFilter={filter}
                    onRecordCreated={handleRecordCreated}
                />
            )}

        </ViewContext.Provider>
    )
}

const PageContextSourceDatabase = ({database}: { database: Database }) => {

    const {databasePageStore, databasePagesId} = useWorkspace()
    const db = databasePageStore[database.id]

    const databaseId = database.id

    const dbItems = useMemo(() => {
        const items: TagItem<undefined>[] = []

        const databaseIds = [...databasePagesId]
        if (!databaseIds.includes(databaseId)) databaseIds.push(databaseId)

        for (const id of databaseIds) {
            const db = databasePageStore[id]
            if (!db) continue
            const {page} = db
            const emoji = page.icon && page.icon.type === ObjectType.Emoji ? page.icon.emoji : '📕'

            const item: TagItem<undefined> = {
                color: undefined, data: undefined, id, title: `${emoji} ${db.page.name}`, value: id
            }
            items.push(item)
        }
        return items
    }, [databasePageStore, databasePagesId, databaseId])

    if (!db) return null;

    const {page} = db
    const emoji = page.icon && page.icon.type === ObjectType.Emoji ? page.icon.emoji : '📕'
    const title = <>{emoji} &nbsp; {db.page.name}</>
    return <>
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="outline" className={cn("text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start max-w-48 items-center")}>
                    <DatabaseIcon className="size-3"/>
                    {/*<span className='text-muted-foreground'>From:</span>*/}
                    <span className='truncate text-black font-medium'>{title}</span>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className='p-0 rounded-none min-w-80' align='start'>
                <div className='p-2 pt-0'>
                    <Label htmlFor="db-select" className="text-xs text-neutral-500">Source database</Label>
                    <CustomSelect
                        onChange={v => {

                        }}
                        disabled
                        selectedIds={[databaseId]}
                        placeholder="Choose a database"
                        options={dbItems}
                    />
                </div>
            </DropdownMenuContent>
        </DropdownMenu>

    </>
}

interface AddRecordPopupProps {
    trigger?: React.ReactNode
    recordId: string
    databaseId: string
    onClose?: () => void
    action?: 'add' | 'peek' | 'edit'
    canEdit?: boolean
}

const PeekRecord = (props: AddRecordPopupProps) => {
    const {databaseStore, members, url} = useWorkspace()
    const template = useMaybeTemplate()
    const shared = useMaybeShared()

    const canExpand = !shared && !template

    const database = databaseStore[props.databaseId]
    const record = databaseStore[props.databaseId].recordsIdMap[props.recordId]
    const onOpenChange = (o: boolean) => {
        if (!o) {
            props.onClose?.()
        }
    }

    if (!database || !record) return null

    const recordInfo = record

    let processedRecord: ProcessedDbRecord | null = null;

    const persons = membersToPersons(members)

    const linkedDatabaseId = Object.values(database.database.definition.columnsMap)
        .filter(c => c.type === DatabaseFieldDataType.Linked && c.databaseId)
        .map(c => (c as LinkedColumn).databaseId)

    const linkedDatabases: LinkedDatabases = {}

    for (const id of linkedDatabaseId) {
        const db = databaseStore[id]
        if (db) {
            linkedDatabases[id] = {
                id,
                definition: db.database.definition,
                recordsMap: {},
                srcPackageName: db.database.srcPackageName
            }
            for (let r of Object.values(db.recordsIdMap)) {
                linkedDatabases[id].recordsMap[r.record.id] = r.record
            }
        }
    }
    const records = [recordInfo.record]
    const processedRecords = transformRawRecords(
        database.database.definition,
        records,
        persons,
        linkedDatabases
    )
    processedRecord = processedRecords[0]

    let href = url(`/databases/${record.record.databaseId}/records/${record.record.id}`)


    return <>
        <Sheet defaultOpen={true} onOpenChange={onOpenChange}>
            <SheetTrigger asChild>
                {props.trigger}
            </SheetTrigger>
            <SheetContent className='!w-[50vw] !min-w-[400px] !max-w-full bg-white p-0 pt-8'>
                {canExpand && <Button variant='ghost' asChild className='absolute right-12 top-2.5 !size-6 !p-1.5 rounded-full'>
                    <Link href={href}>
                        <ArrowUpRightAndArrowDownLeftFromCenterIcon className='size-full'/>
                    </Link>
                </Button>}
                <div className='size-full flex flex-col overflow-hidden'>
                    <SheetHeader className='hidden'>
                        <SheetTitle className='font-bold text-base'>Peek Record</SheetTitle>
                        <SheetDescription className='hidden'>
                            Make changes to your record here. Click save when you're done.
                        </SheetDescription>
                    </SheetHeader>
                    <div className='flex-1 overflow-hidden'>
                        {/*<ScrollArea className='scrollBlockChild'>*/}
                        {/*   */}
                        {/*    /!*<div className="grid gap-4 py-4">*!/*/}
                        {/*    /!*    <div className="grid grid-cols-4 items-center gap-4">*!/*/}
                        {/*    /!*        <Label htmlFor="name" className="text-right">*!/*/}
                        {/*    /!*            Name*!/*/}
                        {/*    /!*        </Label>*!/*/}
                        {/*    /!*        <Input id="name" value="Pedro Duarte" className="col-span-3"/>*!/*/}
                        {/*    /!*    </div>*!/*/}
                        {/*    /!*    <div className="grid grid-cols-4 items-center gap-4">*!/*/}
                        {/*    /!*        <Label htmlFor="username" className="text-right">*!/*/}
                        {/*    /!*            Username*!/*/}
                        {/*    /!*        </Label>*!/*/}
                        {/*    /!*        <Input id="username" value="@peduarte" className="col-span-3"/>*!/*/}
                        {/*    /!*    </div>*!/*/}
                        {/*    /!*</div>*!/*/}
                        {/*</ScrollArea>*/}
                        <RecordTabViewsProvider>
                            <RecordProvider
                                recordInfo={{...record, processedRecord}}
                                database={database.database}>
                                {/*<RecordOverview isTabbed/>*/}
                                <RecordExtras showOverview/>
                                {/*<RecordDetail />*/}
                            </RecordProvider>
                        </RecordTabViewsProvider>
                    </div>
                    <SheetFooter className='hidden'>
                        <SheetClose asChild>
                            <Button type="submit" className='rounded-full'>Save</Button>
                        </SheetClose>
                    </SheetFooter>
                </div>

            </SheetContent>
        </Sheet>
    </>
}

interface ScannableCodeScannerProps {
    database: Database;
    viewFilter: DbRecordFilter;
    filter: DbRecordFilter
    onRecordScan: (id: string) => void
}

const ScannableCodeScanner = ({database, filter, viewFilter, onRecordScan}: ScannableCodeScannerProps) => {
    const {toast} = useAlert()
    const {databaseStore, members, workspace} = useWorkspace()
    const databaseStoreItem = databaseStore[database.id]


    let hasScannableField = false
    for (let value of Object.values(database.definition.columnsMap)) {
        if (value.type === DatabaseFieldDataType.ScannableCode) {
            hasScannableField = true
            break
        }
    }

    if (!hasScannableField) return null
    const onScan = (results: IDetectedBarcode[]) => {
        const detected = results[0]
        if (!detected) {
            toast.error("Nothing to search")
            return
        }

        const rawValue = detected.rawValue
        if (!rawValue) {
            toast.error("Nothing to search")
            return
        }

        const {rows} = filterAndSortRecords(
            databaseStoreItem,
            members,
            databaseStore,
            viewFilter,
            filter,
            [],
            workspace.workspaceMember.userId
        )
        console.log("Scanned value: ", {rawValue, rows})
        const searched = searchFilteredRecords(rawValue, rows)
        if (!searched || searched.length === 0) {
            toast.error("No records found")
            return
        }
        onRecordScan(searched[0].record.id)
    }

    return <>
        <Popover>
            <PopoverTrigger asChild>
                <Button variant="ghost"
                        className="text-xs rounded-full p-1 size-7 gap-2 justify-center items-center font-medium">
                    <BarcodeReadIcon className="size-3"/>
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-4">
                <div>
                    <h4 className='mb-4 text-xs font-medium'>Find record by scanning barcode/QR code</h4>
                </div>
                <div>
                    <Scanner allowMultiple onScan={onScan}/>
                </div>
            </PopoverContent>
        </Popover>
    </>
}

