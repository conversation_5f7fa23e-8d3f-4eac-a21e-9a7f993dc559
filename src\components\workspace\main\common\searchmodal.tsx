"use client"
import { useState, useEffect, use<PERSON><PERSON>back, useRef } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { useWorkspace } from "@/providers/workspace"
import { useAuth } from "@/providers/user"
import { searchWorkspaces } from "@/api/workspace"
import { SearchResult } from "@/typings/workspace"
import { useRouter } from "next/navigation"
import { formatDistanceToNow } from "date-fns"
import debounce from "lodash/debounce"
import {MagnifyingGlassIcon, TimerIcon, BookIcon, TableIcon, UserGroupIcon, XmarkIcon} from "@/components/icons/FontAwesomeRegular"
import { Loader } from "@/components/custom-ui/loader"
import { ViewIcon } from "../views/viewIcon"
import { ViewType } from "opendb-app-db-utils/lib/typings/view"

const SENTINEL_HEIGHT = 32;

interface SearchModalProps { onClose: () => void; debounceTimeoutMS?: number }

const HighlightedContent = ({ content, highlight, query }:
  { content?: string; highlight?: { start: number; end: number }; query?: string }) => {
  if (!content) return null;

  const normalizedContent = content.trim();
  
  if (query && query.trim()) {
    const searchTerm = query.trim().toLowerCase();
    const contentLower = normalizedContent.toLowerCase();
    
    const escapedSearchTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    
    try {
      const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');
      const parts = normalizedContent.split(regex);
      
      return (
        <>
          {parts.map((part, index) => {
            if (part.toLowerCase() === searchTerm) {
              return (
                <mark key={index} className="bg-yellow-200 text-yellow-900 px-0.5 rounded-sm">
                  {part}
                </mark>
              );
            }
            return part;
          })}
        </>
      );
    } catch (error) {
      const parts = [];
      let lastIndex = 0;
      let currentIndex = contentLower.indexOf(searchTerm, lastIndex);

      if (currentIndex === -1) return <>{normalizedContent}</>;
      
      while (currentIndex !== -1) {
        parts.push(normalizedContent.substring(lastIndex, currentIndex));
        parts.push(
          <mark key={currentIndex} className="bg-yellow-200 text-yellow-900 px-0.5 rounded-sm">
            {normalizedContent.substring(currentIndex, currentIndex + searchTerm.length)}
          </mark>
        );
        lastIndex = currentIndex + searchTerm.length;
        currentIndex = contentLower.indexOf(searchTerm, lastIndex);
      }

      if (lastIndex < normalizedContent.length) {
        parts.push(normalizedContent.substring(lastIndex));
      }

      return <>{parts}</>;
    }
  }

  if (highlight && highlight.start >= 0 && highlight.end > highlight.start) {
    const start = Math.max(0, highlight.start);
    const end = Math.min(normalizedContent.length, highlight.end);

    if (start < end && start < normalizedContent.length) {
      return (
        <>
          {normalizedContent.substring(0, start)}
          <mark className="bg-yellow-200 text-yellow-900 px-0.5 rounded-sm">{normalizedContent.substring(start, end)}</mark>
          {normalizedContent.substring(end)}
        </>
      );
    }
  }
  
  return <>{normalizedContent}</>;
};

interface GroupedResults {
  databases: SearchResult[]; pages: SearchResult[]; views: SearchResult[];
  documents: SearchResult[]; members: SearchResult[]; reminders: SearchResult[];
}

const groupResults = (results: SearchResult[]): GroupedResults => {
  const grouped = { databases: [], pages: [], views: [], documents: [], members: [], reminders: [] } as GroupedResults;
  results.forEach(result => {
    if (result.image || !result.source) {
      grouped.members.push(result);
    } else if (result.source) {
      if (result.source.databaseId && !result.source.viewId && !result.source.recordId) grouped.databases.push(result);
      else if (result.source.viewId) grouped.views.push(result);
      else if (result.source.documentId) grouped.documents.push(result);
      else if (result.source.pageId && !result.source.viewId) grouped.pages.push(result);
      else if (result.source.reminderId) grouped.reminders.push(result);
      else grouped.databases.push(result); // Records go to databases
    }
  });
  return grouped;
};

interface ApiError extends Error {
  response?: { data?: { error?: string; message?: string; status?: number } };
  status?: number;
}

export default function SearchModal({ onClose, debounceTimeoutMS = 300 }: SearchModalProps) {
  const [query, setQuery] = useState(""), [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false), [isLoadingMore, setIsLoadingMore] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1), [hasMore, setHasMore] = useState(true)
  const [totalItems, setTotalItems] = useState(0), [currentQuery, setCurrentQuery] = useState("")
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [shouldPreserveScroll, setShouldPreserveScroll] = useState(false)
  const [scrollPosition, setScrollPosition] = useState(0)
  const { workspace, url } = useWorkspace(), { token } = useAuth(), router = useRouter()
  const workspaceId = workspace?.workspace?.id
  const resultsContainerRef = useRef<HTMLDivElement | null>(null)
  const inputRef = useRef<HTMLInputElement | null>(null)
  const sentinelRef = useRef<HTMLDivElement | null>(null)
  const selectedItemRef = useRef<HTMLDivElement | null>(null)
  const loadMoreTimeoutRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    const savedSearches = localStorage.getItem("recentSearches")
    if (savedSearches) setRecentSearches(JSON.parse(savedSearches))
    
    // Focus input on mount
    if (inputRef.current) {
      inputRef.current.focus()
    }

    // Cleanup timeout on unmount
    return () => {
      if (loadMoreTimeoutRef.current) {
        clearTimeout(loadMoreTimeoutRef.current)
      }
    }
  }, [])

  const getErrorMessage = (error: unknown): string => {
    if (error instanceof Error) {
      if ('response' in error) {
        const apiError = error as ApiError;
        return apiError.response?.data?.message || apiError.message;
      }
      return error.message;
    }
    return 'An unexpected error occurred';
  };

  const performSearch = useCallback(async (searchQuery: string, page = 1, append = false) => {
    if (!searchQuery.trim() || !workspaceId || !token) {
      setResults([]); setError(null); setIsLoading(false); setIsLoadingMore(false);
      setHasMore(false); setTotalItems(0); setCurrentPage(1);
      return;
    }

    const currentSearchQuery = searchQuery;

    if (page === 1) {
      setHasMore(true);
      setIsLoading(true); // Only set main loading for first page
    } else {
      setIsLoadingMore(true); // Use separate loading state for subsequent pages
    }
    
    setError(null);

    try {
      const response = await searchWorkspaces(token.token, workspaceId, currentSearchQuery, page, 10);
      if (!response.isSuccess) throw new Error(response.error || 'Search failed');

      const newResults = response.data.data.results.results || [];
      const pagination = response.data.data.results.pagination;

      if (currentSearchQuery === query) {
        setTotalItems(pagination.totalItems);
        setHasMore(page < pagination.totalPages);
      
        setResults(append ? prev => [...prev, ...newResults] : newResults);
        setCurrentPage(page);
        setCurrentQuery(currentSearchQuery);
      }
    } catch (error: unknown) {
      console.error("Search error:", error);
      if (!append && currentSearchQuery === query) {
        setError(getErrorMessage(error));
      }
    } finally {
      if (currentSearchQuery === query) {
        if (page === 1) {
          setIsLoading(false);
        } else {
          setIsLoadingMore(false);
        }
      }
    }
  }, [workspaceId, token, query]);

  const debouncedSearch = useCallback(
    debounce((searchQuery: string) => {
      if (searchQuery.trim()) {
        setCurrentPage(1);
        performSearch(searchQuery, 1, false);
      }
    }, debounceTimeoutMS),
    [performSearch, debounceTimeoutMS, setCurrentPage]
  );

  const loadMore = useCallback(() => {
    if (!isLoading && !isLoadingMore && hasMore && currentQuery) {
      performSearch(currentQuery, currentPage + 1, true);
    }
  }, [isLoading, isLoadingMore, hasMore, currentQuery, currentPage, performSearch]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!results.length && !recentSearches.length) return

      const currentResults = query.trim() ? results : recentSearches.map((search, index) => ({ id: `recent-${index}`, title: search }))
      
      if (e.key === 'ArrowDown') {
        e.preventDefault()
        const nextIndex = Math.min(selectedIndex + 1, currentResults.length - 1)
        
        // Check if we're at the bottom and need to load more
        if (nextIndex === currentResults.length - 1 && hasMore && !isLoading && !isLoadingMore && query.trim()) {
          if (resultsContainerRef.current) {
            setScrollPosition(resultsContainerRef.current.scrollTop)
            setShouldPreserveScroll(true)
          }
          loadMore()
        }
        
        setSelectedIndex(nextIndex)
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setSelectedIndex(prev => Math.max(prev - 1, 0))
      } else if (e.key === 'Enter') {
        e.preventDefault()
        if (currentResults[selectedIndex]) {
          if (query.trim()) {
            handleResultClick(currentResults[selectedIndex] as SearchResult)
          } else {
            const recentSearch = recentSearches[selectedIndex]
            if (recentSearch) {
              setQuery(recentSearch)
            }
          }
        }
      } else if (e.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [results, recentSearches, selectedIndex, query, onClose, hasMore, isLoading, isLoadingMore, loadMore])

  // Handle scroll position preservation and ensure selected item is visible
  useEffect(() => {
    if (shouldPreserveScroll && resultsContainerRef.current && !isLoadingMore) {
      resultsContainerRef.current.scrollTop = scrollPosition
      setShouldPreserveScroll(false)
    }
    
    if (selectedItemRef.current && resultsContainerRef.current) {
      const container = resultsContainerRef.current
      const selectedElement = selectedItemRef.current
      const containerRect = container.getBoundingClientRect()
      const selectedRect = selectedElement.getBoundingClientRect()
      
      if (selectedRect.top < containerRect.top || selectedRect.bottom > containerRect.bottom) {
        selectedElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
      }
    }
  }, [shouldPreserveScroll, scrollPosition, isLoadingMore, selectedIndex])

  // Reset selected index when query changes
  useEffect(() => {
    setSelectedIndex(0)
  }, [query, results])

  // Improved infinite scroll implementation
  useEffect(() => {
    const sentinel = sentinelRef.current;
    const container = resultsContainerRef.current;
    
    if (!sentinel || !container || !hasMore || isLoading || isLoadingMore || results.length === 0) {
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        
        if (entry.isIntersecting && 
            entry.boundingClientRect.top < (entry.rootBounds?.bottom || 0) &&
            hasMore && !isLoading && !isLoadingMore && results.length > 0) {
          
          // Clear any existing timeout
          if (loadMoreTimeoutRef.current) {
            clearTimeout(loadMoreTimeoutRef.current);
          }
          
          // Add a small delay to prevent flash
          loadMoreTimeoutRef.current = setTimeout(() => {
            loadMore();
          }, 100);
        }
      },
      {
        root: container,
        rootMargin: '100px 0px',
        threshold: 0.1
      }
    );

    observer.observe(sentinel); 

    return () => {
      observer.disconnect();
      if (loadMoreTimeoutRef.current) {
        clearTimeout(loadMoreTimeoutRef.current);
      }
    };
  }, [loadMore, hasMore, isLoading, isLoadingMore, results.length, currentPage, totalItems]);

  // Handle search input changes
  useEffect(() => {
    debouncedSearch.cancel();
    setResults([]);
    setCurrentPage(1);
    setHasMore(false);
    setTotalItems(0);
    setCurrentQuery("");
    setIsLoadingMore(false);

    if (query.trim()) {
      setIsLoading(true);
      debouncedSearch(query);
    } else {
      setIsLoading(false);
    }

    return () => debouncedSearch.cancel();
  }, [query, debouncedSearch])

  const handleResultClick = (result: SearchResult) => {
    router.push(url(result.path)); onClose(); saveRecentSearch(query);
  }

  const saveRecentSearch = (search: string) => {
    if (search.trim()) {
      const updatedSearches = [search, ...recentSearches.filter((s) => s !== search)].slice(0, 5)
      setRecentSearches(updatedSearches)
      localStorage.setItem("recentSearches", JSON.stringify(updatedSearches))
    }
  }

  const deleteRecentSearch = (search: string, e: React.MouseEvent) => {
    e.stopPropagation()
    const updatedSearches = recentSearches.filter(s => s !== search)
    setRecentSearches(updatedSearches)
    localStorage.setItem("recentSearches", JSON.stringify(updatedSearches))
  }

  const getIconForSource = (result: SearchResult) => {
    if (result.image) return null;
    if (result.source?.viewId && result.viewType)
      return <ViewIcon type={result.viewType} className="h-4 w-4 text-gray-600" />;
    if (result.source) {
      if (result.source.databaseId) return <TableIcon className="h-4 w-4 text-gray-600" />;
      if (result.source.documentId) return <BookIcon className="h-4 w-4 text-gray-600" />;
      if (result.source.pageId) return <BookIcon className="h-4 w-4 text-gray-600" />;
    }
    return <UserGroupIcon className="h-4 w-4 text-gray-500" />;
  };

  const getResultType = (result: SearchResult) => {
    if (result.path?.includes('?tab=reminders')) return "Reminder";
    if (result.path?.includes('?tab=notes')) return "Note";
    if (result.image) return "Member";
    if (result.source) {
      if (result.source.databaseId) {
        if (result.viewType) {
          switch (result.viewType) {
            case ViewType.Table: return "Table";
            case ViewType.Board: return "Board";
            case ViewType.Dashboard: return "Dashboard";
            case ViewType.Document: return "Page";
            case ViewType.Form: return "Form";
            case ViewType.SummaryTable: return "Summary";
            default: return "View";
          }
        }
        return result.source.recordId ? "Record" : "Database";
      }
      if (result.source.documentId) return "Document";
      if (result.source.pageId) return "Page";
      if (result.source.reminderId) return "Reminder";
    }
    return result.source?.databaseId && result.source.recordId ? "Record" : 
           result.source?.databaseId ? "Database" : 
           result.source?.documentId ? "Document" : 
           result.source?.pageId ? "Page" : "Document";
  };

  const categoryConfig: Record<string, { name: string; icon: JSX.Element }> = {
    databases: { name: "Databases & Records", icon: <TableIcon className="h-4 w-4 text-gray-600" /> },
    pages: { name: "Pages", icon: <BookIcon className="h-4 w-4 text-gray-600" /> },
    views: { name: "Views", icon: <ViewIcon type={ViewType.Table} className="h-4 w-4 text-gray-600" /> },
    documents: { name: "Documents", icon: <BookIcon className="h-4 w-4 text-gray-600" /> },
    members: { name: "People", icon: <UserGroupIcon className="h-4 w-4 text-gray-500" /> },
    reminders: { name: "Reminders", icon: <TimerIcon className="h-4 w-4 text-gray-600" /> }
  };

  const getCategoryName = (category: string) => categoryConfig[category]?.name || category;
  const getCategoryIcon = (category: string) => categoryConfig[category]?.icon || <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />;

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="p-0 gap-0 max-w-xl rounded-lg shadow-xl border border-gray-200 overflow-hidden" hideCloseBtn>
        <div className="flex items-center border-b px-3 relative bg-gray-50">
          <MagnifyingGlassIcon className="mr-2 h-4 w-4 shrink-0 text-gray-500" />
          <Input
            ref={inputRef}
            className="flex h-14 rounded-md border-0 bg-transparent py-3 text-sm outline-none placeholder:text-gray-500 focus-visible:ring-0 disabled:cursor-not-allowed disabled:opacity-50"
            placeholder={`Search in ${workspace?.workspace?.name || 'workspace'}...`}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            autoFocus
          />
          <div className="flex items-center gap-2 text-xs text-gray-500 ml-2">
            <kbd className="px-1.5 py-0.5 bg-gray-100 rounded text-xs font-mono">ESC to close</kbd>
          </div>
        </div>

        <div ref={resultsContainerRef} className="max-h-[60vh] overflow-y-auto pr-1">
          {isLoading && currentPage === 1 ? (
            <div className="px-6 py-8 text-center">
              <Loader className="inline-block w-6 h-6 text-gray-600 animate-spin" />
              <p className="mt-3 text-sm text-gray-600">Searching workspace...</p>
            </div>
          ) : error ? (
            <div className="px-6 py-8 text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100 mb-3">
                <XmarkIcon className="h-5 w-5 text-red-500" />
              </div>
              <h3 className="text-sm font-medium text-gray-900">Search Error</h3>
              <p className="mt-1 text-sm text-red-500">{error}</p>
              <div className="mt-4 flex justify-center gap-3">
                <button 
                  onClick={() => {
                    setError(null);
                    debouncedSearch(query);
                  }}
                  className="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Try again
                </button>
                <button
                  onClick={onClose}
                  className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : !query.trim() ? (
            <div className="px-4 py-4">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2 px-2">Recent Searches</h3>
              {recentSearches.length > 0 ? (
                recentSearches.map((search, index) => (
                  <div 
                    key={index}
                    ref={selectedIndex === index ? selectedItemRef : null}
                    className={`flex items-center justify-between px-2 py-2 cursor-pointer rounded transition-colors ${
                      selectedIndex === index ? 'bg-accent border-l-2 border-primary' : 'hover:bg-gray-50'
                    }`}
                    onClick={() => setQuery(search)}
                  >
                    <div className="flex items-center">
                      <TimerIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-700">{search}</span>
                    </div>
                    <button 
                      onClick={(e) => deleteRecentSearch(search, e)} 
                      className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                    >
                      <XmarkIcon className="h-3 w-3 text-gray-500" />
                    </button>
                  </div>
                ))
              ) : (
                <div className="px-2 py-3 text-sm text-gray-500">
                  No recent searches
                </div>
              )}
            </div>
          ) : results.length === 0 ? (
            <div className="px-6 py-8 text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 mb-3">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-500" />
              </div>
              <h3 className="text-sm font-medium text-gray-900">No results found</h3>
              <p className="mt-1 text-sm text-gray-500">We couldn't find anything matching "{query}"</p>
            </div>
          ) : (
            <>
              {Object.entries(groupResults(results)).map(([category, items]) => 
                items.length > 0 && (
                  <div key={category}>
                    <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-2">
                      {getCategoryName(category)} ({items.length})
                    </h3>
                    {items.map((result: SearchResult, index: number) => {
                      const globalIndex = Object.entries(groupResults(results))
                        .slice(0, Object.keys(groupResults(results)).indexOf(category))
                        .reduce((acc, [, items]) => acc + items.length, 0) + index
                      
                      const isSelected = selectedIndex === globalIndex
                      
                      return (
                        <div
                          key={result.id}
                          ref={isSelected ? selectedItemRef : null}
                          className={`px-3 py-3 cursor-pointer transition-colors flex items-start gap-3 ${
                            isSelected
                              ? 'bg-accent border-l-2 border-primary'
                              : 'hover:bg-gray-50'
                          }`}
                          onClick={() => handleResultClick(result)}
                        >
                          {result.image ? (
                            <img 
                              src={result.image} 
                              alt={result.name || ''}
                              className="h-8 w-8 rounded-full object-cover mt-1"
                            />
                          ) : (
                            <div className="flex-shrink-0 mt-0.5 p-2 rounded-lg bg-gray-100 text-gray-600">
                              {getIconForSource(result)}
                            </div>
                          )}
                          <div className="min-w-0 flex-1">
                            <div className="flex justify-between items-baseline">
                              <h3 className="text-sm font-medium text-gray-900 truncate">
                                <HighlightedContent 
                                  content={result.title || result.name} 
                                  highlight={result.highlight}
                                  query={query}
                                />
                              </h3>
                              {result.publishedAt && (
                                <span className="text-xs text-gray-500 ml-2 whitespace-nowrap">
                                  {formatDistanceToNow(new Date(result.publishedAt), { addSuffix: true })}
                                </span>
                              )}
                            </div>
                            {result.content && (
                              <p className="mt-1 text-xs text-gray-500 line-clamp-2">
                                <HighlightedContent 
                                  content={result.content} 
                                  highlight={result.highlight}
                                  query={query}
                                />
                              </p>
                            )}
                            {result.source && (
                              <div className="mt-1.5">
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                  {getResultType(result)}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )
              )}

              {/* Loading indicator for subsequent pages */}
              {isLoadingMore && (
                <div className="py-4 text-center transition-opacity duration-300 ease-in-out">
                  <Loader className="inline-block w-4 h-4 text-gray-400 animate-spin" />
                  <span className="ml-2 text-sm text-gray-500">Loading more results...</span>
                </div>
              )}

              {/* Infinite scroll sentinel - positioned before the final status messages */}
              {hasMore && !isLoading && !isLoadingMore && (
                <div 
                  ref={sentinelRef} 
                  className="h-8 w-full flex items-center justify-center"
                  style={{ minHeight: SENTINEL_HEIGHT }}
                >
                  <div className="text-xs text-gray-400">• • •</div>
                </div>
              )}

              {/* Final status message */}
              {!isLoading && !isLoadingMore && !hasMore && results.length > 0 && (
                <div className="py-4 text-center text-sm text-gray-500 border-t">
                  {totalItems > 0 ? `Showing all ${totalItems} results` : 'No more results'}
                </div>
              )}
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}